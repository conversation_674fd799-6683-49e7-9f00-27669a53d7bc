import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:optimum_app/src/core/consts/routes/routes.dart';
import 'package:optimum_app/src/core/consts/strings/consts.dart';
import 'package:optimum_app/src/core/consts/theme/theme.dart';
import 'package:optimum_app/src/core/utils/injector/injector.dart';
import 'package:optimum_app/src/features/authentication/presentation/bloc/authentication_bloc.dart';
import 'package:optimum_app/src/features/settings/appearance_settings/appearance_settings.dart';
import 'package:optimum_app/src/features/settings/appearance_settings/components/settings_controller.dart';
import 'package:optimum_app/src/main_screen.dart';

class MyApp extends StatelessWidget {
  const MyApp({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return myApp();
  }

  Widget myApp() {
    final settingsController = sl<SettingsController>();

    MaterialPageRoute<void> onGenerateRoute(RouteSettings routeSettings) {
      return MaterialPageRoute<void>(
        settings: routeSettings,
        builder: (BuildContext context) {
          ScreenUtil.init(context);

          final routes = AppRoutes.routes;
          if (routeSettings.name == AppearanceSettings.routeName) {
            return AppearanceSettings(controller: settingsController);
          } else if (routes.keys.contains(routeSettings.name)) {
            return routes.entries
                .firstWhere((element) => element.key == routeSettings.name)
                .value;
          } else {
            return const MainScreen();
          }
        },
      );
    }

    return BlocProvider(
      create: (context) =>
          sl<AuthenticationBloc>()..add(const AutoLoginEvent()),
      child: AnimatedBuilder(
        animation: settingsController,
        builder: (BuildContext context, Widget? child) {
          return MaterialApp(
            restorationScopeId: 'app',
            debugShowCheckedModeBanner: false,
            localizationsDelegates: Consts.localization,
            supportedLocales: Consts.supportedLocales,
            onGenerateTitle: (BuildContext context) =>
                AppLocalizations.of(context)!.appTitle,
            theme: lightTheme,
            darkTheme: darkTheme,
            locale: settingsController.locale,
            themeMode: settingsController.themeMode,
            initialRoute: '/',
            onGenerateRoute: onGenerateRoute,
          );
        },
      ),
    );
  }
}
