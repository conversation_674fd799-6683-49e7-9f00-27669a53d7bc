import 'dart:io' show Platform;

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:optimum_app/src/core/consts/spaces/spaces.dart';
import 'package:optimum_app/src/core/local_models/tab_model.dart';

import '../consts/theme/theme.dart';

class AdaptiveTabBar extends StatefulWidget {
  final List<TabModel> items;
  final TabController controller;

  const AdaptiveTabBar(
      {super.key, required this.items, required this.controller});

  @override
  State<AdaptiveTabBar> createState() => _AdaptiveTabBarState();
}

class _AdaptiveTabBarState extends State<AdaptiveTabBar> {
  @override
  Widget build(BuildContext context) {
    return Platform.isIOS
        ? CupertinoTabBar(
            backgroundColor: Theme.of(context).scaffoldBackgroundColor,
            onTap: (index) {
              widget.controller.animateTo(index);
              setState(() {});
            },
            currentIndex: widget.controller.index,
            height: 50.h,
            items: widget.items
                .map((e) => BottomNavigationBarItem(
                      icon: e.icon,
                      label: e.title,
                    ))
                .toList())
        : TabBar(
            controller: widget.controller,
            labelPadding:
                const EdgeInsets.symmetric(horizontal: AppSpaces.medium),
            indicatorPadding: EdgeInsets.zero,
            indicatorColor: Colors.white,
            tabAlignment: TabAlignment.start,
            labelColor: Colors.white,
            unselectedLabelColor: appTheme(context).disabledColor,
            physics: const ScrollPhysics(parent: BouncingScrollPhysics()),
            automaticIndicatorColorAdjustment: true,
            labelStyle: labelMediumTextStyle(context).copyWith(
              fontWeight: FontWeight.bold,
            ),
            unselectedLabelStyle: labelMediumTextStyle(context).copyWith(
              fontWeight: FontWeight.bold,
            ),
            isScrollable: true,
            tabs: widget.items
                .map((e) => Tab(
                      icon: e.icon,
                      iconMargin:
                          const EdgeInsets.symmetric(horizontal: AppSpaces.l3),
                      child: Text(e.title),
                    ))
                .toList());
  }
}
