import 'package:barber_app/generated/l10n.dart';
import 'package:flutter/material.dart' show Locale, LocalizationsDelegate;
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:xr_helper/xr_helper.dart';

class AppConsts {
  static const String appName = 'Kingdom Pets';
  static const Locale locale = Locale('ar');

  static const defaultLocation = LatLng(32.4999154, 35.0983419); // Arara

  static const List<Locale> supportedLocales = [
    Locale('ar'),
    locale,
  ];

  static bool get isEnglish =>
      GetStorageService.getData(key: LocalKeys.language) == 'en';

  static const List<LocalizationsDelegate> localizationsDelegates = [
    S.delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  //? Test Login
  static const String testEmail = 'admin';
  static const String testPass = 'test@123';

  //? Payment Gateway (PayPlus)
  static const String payPlusBaseUrl = 'https://restapidev.payplus.co.il';
  static const String payPlusGenerateLinkEndpoint =
      '/api/v1.0/PaymentPages/generateLink';
  static const String payPlusPaymentPageUid =
      '1a0586ac-6527-41f4-bf0e-52c502c4dde6'; //? Barber Demo
  // static const String payPlusPaymentPageUid =
  //     '26df4cbe-84f7-419b-953a-b3504fab9c42';//? Vish Vish Demo
  static const String payPlusApiKey =
      '6548a565-06db-4b2a-8726-427e5907ef58'; //? Barber Demo
  // static const String payPlusApiKey = '52d9814b-ef5a-4856-91ea-8242d9a0b52a';//? Vish Vish Demo API
  static const String payPlusSecretKey =
      'c502e01e-56bd-4826-b120-a3ed8caa1d8e'; //? Barber Demo
  // static const String payPlusSecretKey =
  //     '5356a55d-50fc-4bfb-b6b5-f39a6adedbea'; //? Vish Vish Demo
  static const String payPlusCurrency = 'ILS';

  //? Payment URLs
  static String paymentSuccessUrl(String transactionId) =>
      'https://www.google.com';

  // 'https://yourdomain.com/Successful-Payment/$transactionId';
  static String paymentFailureUrl(String transactionId) =>
      'https://www.facebook.com';
  // 'https://www.google.com/';

  // 'https://yourdomain.com/Failed-Payment/$transactionId';
  static String paymentCallbackUrl(String transactionId) =>
      'https://twitter.com';
// 'https://yourdomain.com/Payment-Process/$transactionId';

  //? SMS4Free API Configuration
  static const String sms4FreeApiUrl = 'https://api.sms4free.co.il/ApiSMS/v2/SendSMS';
  static const String sms4FreeApiKey = 'Aorwz0ECd';
  static const String sms4FreeUser = '0538305911';
  static const String sms4FreePass = '49833434';
  static const String sms4FreeSender = '0538305911';
  static const String sms4FreeRecipient = '0538305911'; // For testing - same as sender
}
