import 'package:flutter/material.dart';
import 'package:optimum_app/src/core/utils/injector/injector.dart';
import 'package:optimum_app/src/features/home/<USER>';
import 'package:optimum_app/src/features/hr/attendance/attendance_details/presentation/pages/attendance_details/attendance_details_page.dart';
import 'package:optimum_app/src/features/hr/attendance/attendance_details/presentation/pages/signature_page/signature_page.dart';
import 'package:optimum_app/src/features/hr/attendance/errands/presentation/pages/add_errand/add_errands_page.dart';
import 'package:optimum_app/src/features/hr/attendance/errands/presentation/pages/errands/errands_page.dart';
import 'package:optimum_app/src/features/hr/attendance/permission_request/presentation/pages/add_permission_request/add_permission_request_page.dart';
import 'package:optimum_app/src/features/hr/attendance/permission_request/presentation/pages/permission_requests/permission_requests_page.dart';
import 'package:optimum_app/src/features/hr/attendance/vacation_request/presentation/pages/add_vacation_request/add_vacation_request_page.dart';
import 'package:optimum_app/src/features/hr/attendance/vacation_request/presentation/pages/vacation_requests/vacation_requests_page.dart';
import 'package:optimum_app/src/features/hr/employees/promotions_and_bonuses/presentation/pages/add_promotions_and_bonuses/add_promotions_and_bonuses_page.dart';
import 'package:optimum_app/src/features/hr/employees/promotions_and_bonuses/presentation/pages/promotions_and_bonuses/promotions_and_bonuses_page.dart';
import 'package:optimum_app/src/features/hr/employees/staff_status/presentation/pages/staff_status/staff_status_page.dart';
import 'package:optimum_app/src/features/hr/main_details/alert_types/presentation/pages/add_alert_types/add_alert_types_page.dart';
import 'package:optimum_app/src/features/hr/main_details/alert_types/presentation/pages/alert_types/alert_types_page.dart';
import 'package:optimum_app/src/features/hr/main_details/borrowing_types/presentation/pages/add_borrowing_types/add_borrowing_types_page.dart';
import 'package:optimum_app/src/features/hr/main_details/borrowing_types/presentation/pages/borrowing_types/borrowing_types_page.dart';
import 'package:optimum_app/src/features/hr/main_details/custodies/presentation/pages/add_custodies/add_custodies_page.dart';
import 'package:optimum_app/src/features/hr/main_details/custodies/presentation/pages/custodies/custodies_page.dart';
import 'package:optimum_app/src/features/hr/main_details/default_employer_salary_items/presentation/pages/default_employer_salary_items_page/default_employer_salary_items_page.dart';
import 'package:optimum_app/src/features/hr/main_details/departments_and_sections/presentation/pages/departments_and_sections_page/departments_and_sections_page.dart';
import 'package:optimum_app/src/features/hr/main_details/discount_types/presentation/pages/add_discount_types/add_discount_types_page.dart';
import 'package:optimum_app/src/features/hr/main_details/discount_types/presentation/pages/discount_types/discount_types_page.dart';
import 'package:optimum_app/src/features/hr/main_details/insurance_companies/presentation/pages/add_insurance_companies/add_insurance_companies_page.dart';
import 'package:optimum_app/src/features/hr/main_details/insurance_companies/presentation/pages/insurance_companies/insurance_companies_page.dart';
import 'package:optimum_app/src/features/hr/main_details/jobs/presentation/pages/add_job/add_jobs_page.dart';
import 'package:optimum_app/src/features/hr/main_details/jobs/presentation/pages/jobs/jobs_page.dart';
import 'package:optimum_app/src/features/hr/main_details/kpis/presentation/pages/add_kpis/add_kpis_page.dart';
import 'package:optimum_app/src/features/hr/main_details/kpis/presentation/pages/kpis/kpis_page.dart';
import 'package:optimum_app/src/features/hr/main_details/medicare_degrees/presentation/pages/add_medicare_degrees/add_medicare_degrees_page.dart';
import 'package:optimum_app/src/features/hr/main_details/medicare_degrees/presentation/pages/medicare_degrees/medicare_degrees_page.dart';
import 'package:optimum_app/src/features/hr/main_details/official_vacations/presentation/pages/add_official_vacations/add_official_vacations_page.dart';
import 'package:optimum_app/src/features/hr/main_details/official_vacations/presentation/pages/official_vacations/official_vacations_page.dart';
import 'package:optimum_app/src/features/hr/main_details/organizations/presentation/pages/add_organizations/add_organizations_page.dart';
import 'package:optimum_app/src/features/hr/main_details/organizations/presentation/pages/training_courses/organizations_page.dart';
import 'package:optimum_app/src/features/hr/main_details/other_costs/presentation/pages/add_other_costs/add_other_costs_page.dart';
import 'package:optimum_app/src/features/hr/main_details/other_costs/presentation/pages/other_costs/other_costs_page.dart';
import 'package:optimum_app/src/features/hr/main_details/permission_types/presentation/pages/add_permission_types/add_permission_types_page.dart';
import 'package:optimum_app/src/features/hr/main_details/permission_types/presentation/pages/permission_types/permission_types_page.dart';
import 'package:optimum_app/src/features/hr/main_details/qualifications/presentation/pages/add_qualification/add_qualifications_page.dart';
import 'package:optimum_app/src/features/hr/main_details/qualifications/presentation/pages/qualifications/qualifications_page.dart';
import 'package:optimum_app/src/features/hr/main_details/reward_types/presentation/pages/add_reward_types/add_reward_types_page.dart';
import 'package:optimum_app/src/features/hr/main_details/reward_types/presentation/pages/reward_types/reward_types_page.dart';
import 'package:optimum_app/src/features/hr/main_details/salary_items/presentation/pages/add_salary_items/add_salary_items_page.dart';
import 'package:optimum_app/src/features/hr/main_details/salary_items/presentation/pages/salary_items/salary_items_page.dart';
import 'package:optimum_app/src/features/hr/main_details/the_media/presentation/pages/add_the_media/add_the_media_page.dart';
import 'package:optimum_app/src/features/hr/main_details/training_courses/presentation/pages/add_training_courses/add_training_courses_page.dart';
import 'package:optimum_app/src/features/hr/main_details/training_courses/presentation/pages/training_courses/training_courses_page.dart';
import 'package:optimum_app/src/features/hr/main_details/vacancy_types/presentation/pages/add_vacancy_types/add_vacancy_types_page.dart';
import 'package:optimum_app/src/features/hr/main_details/vacancy_types/presentation/pages/vacancy_types/vacancy_types_page.dart';
import 'package:optimum_app/src/features/hr/regulations/presentation/pages/add_regulation/add_regulations_page.dart';
import 'package:optimum_app/src/features/hr/regulations/presentation/pages/regulations/regulations_page.dart';
import 'package:optimum_app/src/features/hr/salaries/alerts/alerts_imports.dart';
import 'package:optimum_app/src/features/hr/salaries/discountsIssuances/discountsIssuances_imports.dart';
import 'package:optimum_app/src/features/hr/salaries/employeeBorrows/employeeBorrows_imports.dart';
import 'package:optimum_app/src/features/hr/salaries/hr_vouchers/presentation/pages/add_hr_voucher/add_hr_vouchers_page.dart';
import 'package:optimum_app/src/features/hr/salaries/hr_vouchers/presentation/pages/hr_vouchers/hr_vouchers_page.dart';
import 'package:optimum_app/src/features/hr/salaries/rewardsIssuances/rewardsIssuances_imports.dart';
import 'package:optimum_app/src/features/hr/shifts/presentation/pages/add_shift/add_shifts_page.dart';
import 'package:optimum_app/src/features/hr/shifts/presentation/pages/shifts/shifts_page.dart';
import 'package:optimum_app/src/features/hr/vacatation_allowance/approved_leave_work_eligibles/presentation/pages/approved_leave_work_eligibles/approved_leave_work_eligibles_page.dart';
import 'package:optimum_app/src/features/hr/vacatation_allowance/eligible_employees/presentation/pages/eligible_employees_page.dart';
import 'package:optimum_app/src/features/hr/vacatation_allowance/vacation_allowance_settings/vacation_allowance_settings.dart';
import 'package:optimum_app/src/features/hr/violations/presentation/pages/add_violations/add_violations_page.dart';
import 'package:optimum_app/src/features/hr/violations/presentation/pages/violations/violations_page.dart';
import 'package:optimum_app/src/features/management/annuals/presentation/annuals/annuals_screen.dart';
import 'package:optimum_app/src/features/management/clients/presentation/clients_page.dart';
import 'package:optimum_app/src/features/management/clients/presentation/widgets/add_client_page.dart';
import 'package:optimum_app/src/features/management/companies_and_branches/presentation/companies_branches.dart';
import 'package:optimum_app/src/features/management/countries_states/presentation/countries/countries_screen.dart';
import 'package:optimum_app/src/features/management/currencies/presentation/pages/currencies_page/currencies_page.dart';
import 'package:optimum_app/src/features/management/representatives/presentation/pages/add_representative/add_representatives_page.dart';
import 'package:optimum_app/src/features/management/representatives/presentation/pages/representatives/representatives_page.dart';
import 'package:optimum_app/src/features/management/suppliers/presentation/pages/add_supplier/add_supplier_page.dart';
import 'package:optimum_app/src/features/management/suppliers/presentation/pages/suppliers/suppliers_page.dart';
import 'package:optimum_app/src/features/management/users/presentation/pages/add_user/add_user_page.dart';
import 'package:optimum_app/src/features/management/users/presentation/pages/user_activities/user_activities_page.dart';
import 'package:optimum_app/src/features/management/users/presentation/pages/users_page/users_page.dart';
import 'package:optimum_app/src/features/reports/hr_reports/attendance_reports/presentation/pages/attendance_reports.dart';
import 'package:optimum_app/src/features/reports/hr_reports/bonuses_report/presentation/pages/bonuses_reports.dart';
import 'package:optimum_app/src/features/reports/hr_reports/leave_work_report/presentation/pages/leave_work_reports.dart';
import 'package:optimum_app/src/features/reports/hr_reports/main_details_report/presentation/pages/main_details_reports.dart';
import 'package:optimum_app/src/features/settings/appearance_settings/appearance_settings.dart';
import 'package:optimum_app/src/features/settings/appearance_settings/components/settings_controller.dart';
// import 'package:optimum_app/src/features/transport/transport_port_notifications/group_transport_notifications/presentation/group_transport_notification.dart';
import 'package:optimum_app/src/main_screen.dart';

import '../../../features/authentication/presentation/pages/login/new_login_screen.dart';
import '../../../features/authentication/presentation/pages/login_page/login_screen.dart';
import '../../../features/hr/employees/cvs/cvs_imports.dart';
import '../../../features/hr/employees/employees_list/employees_imports.dart';
import '../../../features/hr/main_details/the_media/presentation/pages/the_media/the_media_page.dart';
import '../../../features/management/annuals/presentation/add_annual/add_annual_page.dart';
import '../../../features/management/currencies/presentation/pages/add_currency/add_currency_page.dart';
import '../../../features/management/currencies/presentation/pages/currency_prices/currency_price.dart';
import '../../../features/management/roles/presentation/pages/add_role/add_roles_page.dart';
import '../../../features/management/roles/presentation/pages/roles_page/roles_page.dart';
import '../../../features/settings/public_settings/presentation/pages/public_settings/public_settings_page.dart';
import '../../shared_widgets/login_loading.dart';

part 'app_routes.dart';
