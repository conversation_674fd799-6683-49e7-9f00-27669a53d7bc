class ApiStrings {
  static String baseUrl(String? subdomain) =>
      "http://$subdomain.optierp.net";
  //http://dev.opti4it.com

  // static String apiUrl(String? subdomain) => "https://oceansa.optierp.net/api/v1/";
  static String apiUrl(String? subdomain) => "${baseUrl(subdomain)}/api/v1/";

  //? Pusher Strings
  static const String pusherKey = "optimumerpprod";

  //? Api Strings
  static const String data = "data";
  static const String departments = "departments";
}
