part of 'extensions.dart';

extension ThemeExtensions on BuildContext {
  //? Theme
  ThemeData get theme => Theme.of(this);

  //? Get the current theme
  bool get isDark => theme.brightness == Brightness.dark;

  //? Get card color
  Color get cardColor =>
      isDark ? ColorManager.cardColorDark() : ColorManager.cardColorLight();

  //? Get color shortcuts
  Color get primaryColor => ColorManager.primaryColor;

  Color get accentColor => ColorManager.secondaryColor;

  //? Get text style shortcuts
  TextStyle get headLine => headLineTextStyle(this);

  TextStyle get titleStyle => titleTextStyle(this);

  TextStyle get subTitleStyle => subtitleTextStyle(this);

  TextStyle get labelLargeStyle => labelLargeTextStyle(this);

  TextStyle get labelMediumStyle => labelMediumTextStyle(this);

  TextStyle get labelSmallStyle => labelSmallTextStyle(this);

  TextStyle get bodyStyle => bodyTextStyle(this);

  TextStyle get hintStyle => hintTextStyle(this);
}

extension Localization on BuildContext {
  //? Get the current language
  AppLocalizations get local => AppLocalizations.of(this)!;

  //? Get the current language code
  String get languageCode => Localizations.localeOf(this).languageCode;

  //? Get the current language is Arabic
  bool get isEng => languageCode == 'en';
}

extension NavigationExtensions on BuildContext {
  void to(Widget widget) => Navigator.of(this).push(
        MaterialPageRoute(builder: (_) => widget),
      );

  void toReplacement(Widget widget) => Navigator.of(this).pushReplacement(
        MaterialPageRoute(builder: (_) => widget),
      );

  void toNamed(String routeName) => Navigator.of(this).pushNamed(routeName);

  void toNamedReplacement(String routeName) =>
      Navigator.of(this).pushReplacementNamed(routeName);

  void back() => Navigator.of(this).pop();
}

extension SizeExensions on BuildContext {
  //? Get size shortcuts
  double get height => MediaQuery.sizeOf(this).height;

  double get width => MediaQuery.sizeOf(this).width;

  //? Get space shortcuts -- Horizontal & Vertical

  Gap get smallGap => const Gap(5);

  Gap get mediumGap => const Gap(10);

  Gap get largeGap => const Gap(20);

  Gap get xLargeGap => const Gap(30);

  Gap get xxLargeGap => const Gap(40);

  //! Field Space
  Gap get fieldSpace => const Gap(15);
}

extension ShowBars on BuildContext {
  //? Alerts
  void showBar(String msg, {bool isError = false}) =>
      showBarAlert(this, msg, isError: isError);

  //? Alerts
  void showNotification(
      {String title = '',
      String msg = '',
      NotificationType notificationType = NotificationType.info}) {
    late final Color color;

    switch (notificationType) {
      case NotificationType.info:
        color = ColorManager.secondaryColor;
        break;
      case NotificationType.warning:
        color = ColorManager.orange;
        break;
      case NotificationType.error:
        color = ColorManager.red;
        break;
    }

    showBarAlert(
      this,
      msg,
      title: title,
      icon: Icons.notifications,
      isError: notificationType == NotificationType.error,
      color: color,
    );
  }
}
