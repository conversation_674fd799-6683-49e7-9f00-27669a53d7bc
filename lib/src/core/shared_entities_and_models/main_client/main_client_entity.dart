import 'package:equatable/equatable.dart';

import 'main_client_model.dart';

class MainClientEntity extends Equatable {
  final int? id;
  final String? nameAr;
  final String? nameEn;
  final int? countryId;
  final int? stateId;
  final String? taxNumber;
  final String? email;
  final String? phone;
  final String? phone2;
  final String? fax;
  final String? address;
  final String? website;
  final String? city;
  final String? status;
  final int? repId;
  final int? accountId;
  final int? creditLimit;
  final int? creditDays;
  final String? notes;
  final String? createdAt;
  final String? updatedAt;
  final String? deletedAt;
  final String? commercialRegistrationNo;
  final String? registrationExpireDate;
  final int? branchId;
  final int? allBranches;
  final int? groupId;
  final String? name;

  const MainClientEntity({
    this.id,
    this.nameAr,
    this.nameEn,
    this.countryId,
    this.stateId,
    this.taxNumber,
    this.email,
    this.phone,
    this.phone2,
    this.fax,
    this.address,
    this.website,
    this.city,
    this.status,
    this.repId,
    this.accountId,
    this.creditLimit,
    this.creditDays,
    this.notes,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.commercialRegistrationNo,
    this.registrationExpireDate,
    this.branchId,
    this.allBranches,
    this.groupId,
    this.name,
  });

  static MainClientModel toModel(MainClientEntity client) => MainClientModel(
        id: client.id,
        nameAr: client.nameAr,
        nameEn: client.nameEn,
        countryId: client.countryId,
        stateId: client.stateId,
        taxNumber: client.taxNumber,
        email: client.email,
        phone: client.phone,
        phone2: client.phone2,
        fax: client.fax,
        address: client.address,
        website: client.website,
        city: client.city,
        status: client.status,
        repId: client.repId,
        accountId: client.accountId,
        creditLimit: client.creditLimit,
        creditDays: client.creditDays,
        notes: client.notes,
        createdAt: client.createdAt,
        updatedAt: client.updatedAt,
        deletedAt: client.deletedAt,
        commercialRegistrationNo: client.commercialRegistrationNo,
        registrationExpireDate: client.registrationExpireDate,
        branchId: client.branchId,
        allBranches: client.allBranches,
        groupId: client.groupId,
        name: client.name,
      );

  @override
  List<Object?> get props => [
        id,
        nameAr,
        nameEn,
        countryId,
        stateId,
        taxNumber,
        email,
        phone,
        phone2,
        fax,
        address,
        website,
        city,
        status,
        repId,
        accountId,
        creditLimit,
        creditDays,
        notes,
        createdAt,
        updatedAt,
        deletedAt,
        commercialRegistrationNo,
        registrationExpireDate,
        branchId,
        allBranches,
        groupId,
        name,
      ];
}
