import 'package:equatable/equatable.dart';

class MainNamesEntity extends Equatable {
  final String? processNum;
  final String? processName;
  final String? processDescription;
  final int? branchId;

  const MainNamesEntity(
      {this.processNum,
      this.processName,
      this.processDescription,
      this.branchId});

  static MainNamesModel toModel(MainNamesEntity namesEntity) => MainNamesModel(
        processNum: namesEntity.processNum,
        processName: namesEntity.processName,
        processDescription: namesEntity.processDescription,
        branchId: namesEntity.branchId,
      );

  @override
  List<Object?> get props {
    return [
      processNum,
      processName,
      processDescription,
      branchId,
    ];
  }
}

class MainNamesModel extends MainNamesEntity {
  const MainNamesModel(
      {super.processNum,
      super.processName,
      super.processDescription,
      super.branchId});

  @override
  List<Object?> get props {
    return [
      processNum,
      processName,
      processDescription,
      branchId,
    ];
  }

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'processNum': processNum,
      'processName': processName,
      'processDescription': processDescription,
      'branchId': branchId,
    };
  }

  factory MainNamesModel.fromJson(Map<String, dynamic> json) {
    return MainNamesModel(
      processNum: json['processNum'],
      processName: json['processName'],
      processDescription: json['processDescription'],
      branchId: json['branchId'],
    );
  }
}
