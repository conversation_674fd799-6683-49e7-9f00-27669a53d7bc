import 'package:equatable/equatable.dart';

class MainNotificationDetailTypeEntity extends Equatable {
  final int? id;
  final String? name;
  final String? detail;
  final String? createdAt;
  final String? updatedAt;

  const MainNotificationDetailTypeEntity(
      {this.id, this.name, this.detail, this.createdAt, this.updatedAt});

  static MainNotificationDetailTypeModel toModel(
      MainNotificationDetailTypeEntity notificationDetailType) =>
      MainNotificationDetailTypeModel(
        id: notificationDetailType.id,
        name: notificationDetailType.name ,
        detail: notificationDetailType.detail ,
        createdAt: notificationDetailType.createdAt ,
        updatedAt: notificationDetailType.updatedAt ,
      );

  @override
  List<Object?> get props {
    return [
      id,
      name,
      detail,
      createdAt,
      updatedAt,
    ];
  }
}


class MainNotificationDetailTypeModel extends MainNotificationDetailTypeEntity {
  const MainNotificationDetailTypeModel(
      {super.id, super.name, super.detail, super.createdAt, super.updatedAt});

  @override
  List<Object?> get props {
    return [
      id,
      name,
      detail,
      createdAt,
      updatedAt,
    ];
  }

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'id': id,
      'name': name,
      'detail': detail,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  factory MainNotificationDetailTypeModel.fromJson(Map<String, dynamic> map) {
    return MainNotificationDetailTypeModel(
      id: map['id'],
      name: map['name'],
      detail: map['detail'] ,
      createdAt: map['createdAt'] ,
      updatedAt: map['updatedAt'],
    );
  }
}
