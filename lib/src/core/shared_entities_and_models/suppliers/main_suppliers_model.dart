import 'main_supplier_entity.dart';

class MainSupplierModel extends MainSupplierEntity {
  const MainSupplierModel(
      {super.id,
      super.nameAr,
      super.nameEn,
      super.status,
      super.isTransporter,
      super.countryId,
      super.email,
      super.phone,
      super.phone2,
      super.fax,
      super.taxNumber,
      super.registrationNumber,
      super.creditLimit,
      super.creditDays,
      super.accountId,
      super.notes,
      super.createdAt,
      super.updatedAt,
      super.deletedAt,
      super.createSupplierAccount,
      super.parentId,
      super.level,
      super.name});

  factory MainSupplierModel.fromJson(Map<String, dynamic> json) {
    return MainSupplierModel(
      id: json['id'],
      nameAr: json['name_ar'],
      nameEn: json['name_en'],
      status: json['status'],
      isTransporter: json['is_transporter'],
      countryId: json['country_id'],
      email: json['email'],
      phone: json['phone'],
      phone2: json['phone2'],
      fax: json['fax'],
      taxNumber: json['tax_number'],
      registrationNumber: json['registration_number'],
      creditLimit: json['credit_limit'],
      creditDays: json['credit_days'],
      accountId: json['account_id'],
      notes: json['notes'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
      deletedAt: json['deleted_at'],
      name: json['name'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (id != null) {
      data['id'] = id;
    }
    data['create_account'] = 0;
    data['name_ar'] = nameAr;
    data['name_en'] = nameEn;
    data['status'] = status;
    data['is_transporter'] = isTransporter;
    data['level'] = level;
    data['email'] = email;
    data['phone'] = phone;
    data['notes'] = notes;
    return data;
  }
}
