part of '../shared_widgets.dart';

class BaseSaveButton extends StatelessWidget {
  final void Function()? onPressed;

  const BaseSaveButton({
    Key? key,
    required this.onPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final local = AppLocalizations.of(context)!;

    return Material(
      elevation: 16,
      child: Padding(
        padding: const EdgeInsets.symmetric(
            horizontal: AppSpaces.xxLarge, vertical: AppSpaces.large),
        child: BaseButton(
          label: local.save,
          icon: const BaseLottieIcon(
            AnimatedAssets.add,
          ),
          isPrefixIcon: true,
          onPressed: onPressed,
        ),
      ),
    );
  }
}
