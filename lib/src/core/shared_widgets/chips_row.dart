import 'package:flutter/material.dart';
import 'package:optimum_app/src/core/consts/spaces/spaces.dart';

class ChipsRow extends StatelessWidget {
  final List<String> children;
  const ChipsRow({super.key, required this.children});

  @override
  Widget build(BuildContext context) {
    return Material(
      elevation: 5,
      color: Theme.of(context).primaryColor,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: AppSpaces.medium / 2),
          child: Row(
            children: children
                .map((e) => Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: AppSpaces.medium / 3),
                      child: Chip(
                          label: Text(
                        e,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      )),
                    ))
                .toList(),
          ),
        ),
      ),
    );
  }
}
