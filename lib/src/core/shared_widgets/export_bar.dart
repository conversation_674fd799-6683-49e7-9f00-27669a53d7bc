import 'dart:io' show Platform;

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:optimum_app/src/core/adaptive_widgets/adaptiive_button.dart';
import 'package:optimum_app/src/core/consts/spaces/spaces.dart';

class ExportBar extends StatelessWidget {
  final String? email;
  final dynamic file;

  const ExportBar({super.key, this.email, this.file});

  @override
  Widget build(BuildContext context) {
    final txt = AppLocalizations.of(context)!;

    final isIOS = Platform.isIOS;

    return Padding(
      padding: const EdgeInsets.all(AppSpaces.medium),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          AdaptiveButton(
            onPressed: () {},
            icon: isIOS ? CupertinoIcons.printer : Icons.print,
            child: Text(txt.print),
          ),
          AdaptiveButton(
            onPressed: () {},
            icon: isIOS ? CupertinoIcons.share : Icons.exit_to_app_rounded,
            child: Text(txt.export),
          ),
          AdaptiveButton(
            onPressed: () {},
            icon: isIOS ? CupertinoIcons.mail_solid : Icons.email,
            child: Text(txt.sendEmail),
          ),
        ],
      ),
    );
  }
}
