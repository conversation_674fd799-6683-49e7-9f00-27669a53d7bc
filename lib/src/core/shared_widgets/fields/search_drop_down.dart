part of '../shared_widgets.dart';

const _radius = Radius.circular(12);

class BaseSearchDropDown extends HookWidget {
  final dynamic selectedValue;
  final String? label;
  final List<dynamic> data;
  final void Function(dynamic)? onChanged;
  final Widget? icon;
  final bool isRequired;
  final bool isMultiSelect;
  final bool ignoring;
  final String? ignoringMessage;
  final Function(dynamic)? itemModelAsName;
  final Function(dynamic)? multiItemsAsName;

  const BaseSearchDropDown(
      {super.key,
      required this.onChanged,
      required this.data,
      required this.label,
      required this.selectedValue,
      this.isRequired = true,
      this.isMultiSelect = false,
      this.ignoring = false,
      this.itemModelAsName,
      this.multiItemsAsName,
      this.ignoringMessage,
      this.icon});

  @override
  Widget build(BuildContext context) {
    final isDropdownOpen = useState(false);

    return IgnorePointer(
      ignoring: ignoring,
      child: Wrap(
        children: [
          if (label != null)
            Padding(
              padding: EdgeInsets.only(
                left: 4.w,
                bottom: 6.h,
              ),
              child: FittedBox(
                fit: BoxFit.scaleDown,
                child: Row(
                  children: [
                    Text(label!, style: context.labelLargeStyle),
                    if (isRequired) ...[
                      SizedBox(width: 4.w),
                      Text('*',
                          style: context.labelLargeStyle
                              .copyWith(color: Colors.red)),
                    ]
                  ],
                ),
              ),
            ),
          if (ignoringMessage != null)
            Padding(
              padding: EdgeInsets.only(
                left: 4.w,
                bottom: 6.h,
              ),
              child: FittedBox(
                fit: BoxFit.scaleDown,
                child: Text(ignoringMessage!,
                    style: context.labelMediumStyle
                        .copyWith(color: Colors.red.shade600)),
              ),
            ),
          Material(
            elevation: 2,
            borderRadius: isDropdownOpen.value
                ? const BorderRadius.only(topLeft: _radius, topRight: _radius)
                : BorderRadius.circular(12),
            color: context.isDark ? ColorManager.cardColorDark() : Colors.white,
            child: isMultiSelect
                ? _multiSelect(context, isDropdownOpen: isDropdownOpen)
                : _singleSelect(context, isDropdownOpen: isDropdownOpen),
          ),
        ],
      ),
    );
  }

  Widget _singleSelect(BuildContext context,
      {required ValueNotifier<bool> isDropdownOpen}) {
    final local = AppLocalizations.of(context)!;

    return DropdownSearch(
      onBeforePopupOpening: (controller) {
        isDropdownOpen.value = true;
        return Future.value(true);
      },
      onBeforeChange: (val, value) {
        isDropdownOpen.value = false;
        return Future.value(true);
      },
      autoValidateMode: AutovalidateMode.onUserInteraction,
      validator: (value) {
        if (value == null && selectedValue == null && isRequired) {
          return '${local.pleaseSelect} $label';
        }
        return null;
      },
      selectedItem: selectedValue,
      popupProps: PopupProps.menu(
        onDismissed: () {
          isDropdownOpen.value = false;
        },
        menuProps: MenuProps(
          elevation: 2,
          backgroundColor:
              context.isDark ? ColorManager.cardColorDark() : Colors.white,
          borderRadius: !isDropdownOpen.value
              ? const BorderRadius.only(
                  bottomLeft: _radius,
                  bottomRight: _radius,
                )
              : BorderRadius.circular(12),
        ),
        itemBuilder: (context, data, isSelected) {
          return Column(
            children: [
              ListTile(
                selected: isSelected,
                title: Text(
                  itemModelAsName != null
                      ? itemModelAsName!(data)
                      : data.toString(),
                ),
              ),
              const Divider(
                thickness: .4,
              ),
            ],
          );
        },
        isFilterOnline: false,
        showSelectedItems: false,
        searchFieldProps: TextFieldProps(
          decoration: InputDecoration(
            border: InputBorder.none,
            hintText: local.search,
          ),
        ),
        showSearchBox: true,
      ),
      dropdownBuilder: (context, value) {
        if (value == null && selectedValue == null) {
          return Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Text(
              '${local.select} $label',
            ),
          );
        }

        return Padding(
          padding: const EdgeInsets.only(top: 4),
          child: Text(
            itemModelAsName != null
                ? itemModelAsName!(value)
                : '${value ?? selectedValue ?? '${local.select} $label'}',
          ),
        );
      },
      dropdownDecoratorProps: DropDownDecoratorProps(
        dropdownSearchDecoration: InputDecoration(
          contentPadding: EdgeInsets.symmetric(
            vertical: 12.h,
            horizontal: icon == null ? 16.w : 0,
          ),
          icon: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: AppSpaces.small,
            ),
            child: icon,
          ),
          border: InputBorder.none,
          labelText: label,
          labelStyle: context.labelMediumStyle.copyWith(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
          filled: true,
          fillColor: Colors.transparent,
        ),
      ),
      items: data,
      onChanged: onChanged,
    );
  }

  Widget _multiSelect(BuildContext context,
      {required ValueNotifier<bool> isDropdownOpen}) {
    final local = AppLocalizations.of(context)!;

    return DropdownSearch.multiSelection(
      onBeforePopupOpening: (controller) {
        isDropdownOpen.value = true;
        return Future.value(true);
      },
      onBeforeChange: (val, value) {
        isDropdownOpen.value = false;
        return Future.value(true);
      },
      autoValidateMode: AutovalidateMode.onUserInteraction,
      validator: (value) {
        if (value == null && selectedValue == null && isRequired) {
          return '${local.pleaseSelect} $label';
        }
        return null;
      },
      popupProps: PopupPropsMultiSelection.menu(
        onDismissed: () {
          isDropdownOpen.value = false;
        },
        menuProps: MenuProps(
          elevation: 2,
          backgroundColor:
              context.isDark ? ColorManager.cardColorDark() : Colors.white,
          borderRadius: !isDropdownOpen.value
              ? const BorderRadius.only(
                  bottomLeft: _radius,
                  bottomRight: _radius,
                )
              : BorderRadius.circular(12),
        ),
        itemBuilder: (context, data, isSelected) {
          return Column(
            children: [
              ListTile(
                selected: isSelected,
                title: Text(
                  itemModelAsName != null
                      ? itemModelAsName!(data)
                      : data.toString(),
                ),
              ),
              const Divider(
                thickness: .4,
              ),
            ],
          );
        },
        isFilterOnline: false,
        showSelectedItems: false,
        searchFieldProps: TextFieldProps(
          decoration: InputDecoration(
            border: InputBorder.none,
            hintText: local.search,
          ),
        ),
        showSearchBox: true,
      ),
      dropdownBuilder: (context, value) {
        if (selectedValue == null) {
          return Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Text(
              '${local.select} $label',
            ),
          );
        }
        return Padding(
          padding: const EdgeInsets.only(top: 4),
          child: Text(
            multiItemsAsName != null ? multiItemsAsName!(value) : '$value',
          ),
        );
      },
      dropdownDecoratorProps: DropDownDecoratorProps(
        dropdownSearchDecoration: InputDecoration(
          contentPadding: EdgeInsets.symmetric(
            vertical: 12.h,
            horizontal: icon == null ? 16.w : 0,
          ),
          icon: Padding(
            padding: const EdgeInsets.only(left: AppSpaces.small),
            child: icon,
          ),
          border: InputBorder.none,
          labelText: label,
          labelStyle: context.labelMediumStyle.copyWith(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
          filled: true,
          fillColor: Colors.transparent,
        ),
      ),
      items: data,
      onChanged: onChanged,
    );
  }
}
