part of '../../shared_widgets.dart';

class CellWidget extends StatelessWidget {
  final String label;
  final bool isTitle;
  final bool isWhite;

  const CellWidget(this.label,
      {this.isTitle = false, this.isWhite = false, Key? key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: isTitle ? 135.w : 140.w,
      child: Center(
        child: Text(
          label,
          style: isTitle
              ? context.subTitleStyle.copyWith(
                  fontWeight: FontWeight.w600,
                  color:
                      isWhite || context.isDark ? Colors.white : Colors.black,
                )
              : context.labelMediumStyle.copyWith(
                  color:
                      isWhite || context.isDark ? Colors.white : Colors.black,
                ),
          textAlign: TextAlign.center,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
      ),
    );
  }
}

DataRow textCell({
  required String centerText,
  String? titleText,
  Color? color,
  int prevEmptyCells = 0,
  int centerEmptyCells = 0,
  int nextEmptyCells = 1,
}) {
  const emptyCell = DataCell(
    SizedBox.shrink(),
  );

  DataCell cellWidget(String text) => DataCell(
        Container(
          alignment: Alignment.center,
          margin: const EdgeInsets.symmetric(vertical: 5),
          decoration: BoxDecoration(
            color: color ?? ColorManager.lightGreyColor,
            borderRadius: BorderRadius.circular(5),
          ),
          child: CellWidget(
            text,
            isTitle: true,
            isWhite: true,
          ),
        ),
      );

  return DataRow(cells: [
    for (var i = 0; i < prevEmptyCells; i++) emptyCell,
    if (titleText != null) cellWidget(titleText),
    for (var i = 0; i < centerEmptyCells; i++) emptyCell,
    cellWidget(centerText),
    for (var i = 0; i < nextEmptyCells; i++) emptyCell,
  ]);
}
