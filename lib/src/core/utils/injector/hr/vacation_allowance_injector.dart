part of injector;

void _hrVacationAllowanceInjector() async {
  //? ----------------- Vacation Allowance -----------------
  //! Cubit
  sl.registerFactory(() => VacationAllowancesCubit(
        getVacationAllowancesUseCase: sl(),
        updateVacationAllowancesUseCase: sl(),
      ));

  //! Use cases
  sl.registerLazySingleton(() => GetVacationAllowancesUseCase(sl()));
  sl.registerLazySingleton(() => UpdateVacationAllowancesUseCase(sl()));

  //! Repository
  sl.registerLazySingleton<VacationAllowancesRepository>(
      () => VacationAllowancesRepositoryImpl(remoteDataSource: sl()));

  //! Data sources
  sl.registerLazySingleton<VacationAllowancesRemoteSource>(
      () => VacationAllowancesRemoteSourceImpl(sl<NetworkApiService>()));

  //? ----------------- Eligible Employees -----------------
  //! Cubit
  sl.registerFactory(() => EligibleEmployeesCubit(
        getAllEligibleEmployeesUseCase: sl(),
      ));

  //! Use cases
  sl.registerLazySingleton(() => GetEligibleEmployeesUseCase(sl()));

  //! Repository
  sl.registerLazySingleton<EligibleEmployeesRepository>(
      () => EligibleEmployeesRepositoryImpl(remoteDataSource: sl()));

  //! Data sources
  sl.registerLazySingleton<EligibleEmployeesRemoteDataSource>(() =>
      EligibleEmployeesRemoteDataSourceImpl(
          apiService: sl<NetworkApiService>()));

  //? ----------------- Approved Leave Work Eligibles -----------------
  //! Cubit
  sl.registerFactory(() => ApprovedLeaveWorkEligiblesCubit(
        getAllApprovedLeaveWorkEligiblesUseCase: sl(),
      ));

  //! Use cases
  sl.registerLazySingleton(() => GetApprovedLeaveWorkEligiblesUseCase(sl()));

  //! Repository
  sl.registerLazySingleton<ApprovedLeaveWorkEligiblesRepository>(
      () => ApprovedLeaveWorkEligiblesRepositoryImpl(remoteDataSource: sl()));

  //! Data sources
  sl.registerLazySingleton<ApprovedLeaveWorkEligiblesRemoteDataSource>(() =>
      ApprovedLeaveWorkEligiblesRemoteDataSourceImpl(sl<NetworkApiService>()));
}
