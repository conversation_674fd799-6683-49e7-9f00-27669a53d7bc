import 'dart:developer';

import 'package:dartz/dartz.dart';
import 'package:optimum_app/src/core/data/remote/network/api_end_points.dart';
import 'package:optimum_app/src/core/data/remote/network/network_api_service.dart';

import '../models/accounts_trees_model.dart';

abstract class AccountsTreeRemoteDataSource {
  Future<List<AccountTreeModel>> getSearchedAccountsTrees(
      {String? search, int? level});
  Future<List<AccountTreeModel>> getAllAccountTrees();
  Future<Unit> addAccountTree(AccountTreeModel accountsTreeModel);
  Future<Unit> updateAccountTree(AccountTreeModel accountsTreeModel);
}

class AccountsTreeRemoteDataSourceImpl extends AccountsTreeRemoteDataSource {
  final _apiService = NetworkApiService();

  @override
  Future<List<AccountTreeModel>> getSearchedAccountsTrees(
      {String? search, int? level}) async {
    final endPoint =
        '${ApiEndPoints.accountsTrees}/search/${search ?? 'all'}?level=${level ?? 6}';
    try {
      final response = await _apiService.getResponse(
        endPoint,
      );

      log('AccountsTreeRemoteDataSourceImplUrl: $endPoint');
      final accountsTrees = List<AccountTreeModel>.from(
        response['data']['accounts'].map(
          (x) => AccountTreeModel.fromJson(x),
        ),
      );

      return accountsTrees;
    } catch (error) {
      log('AccountsTreeRemoteDataSourceImplError: $error');
      rethrow;
    }
  }

  @override
  Future<List<AccountTreeModel>> getAllAccountTrees() async {
    const endPoint = ApiEndPoints.accountsTrees;
    try {
      final response = await _apiService.getResponse(
        endPoint,
      );

      log('AccountsTreeRemoteDataSourceImplUrl: $endPoint');
      final accountsTrees = List<AccountTreeModel>.from(
        response['data']['accounts'].map(
          (x) => AccountTreeModel.fromJson(x),
        ),
      );

      return accountsTrees;
    } catch (error) {
      log('AccountsTreeRemoteDataSourceImplError: $error');
      rethrow;
    }
  }

  @override
  Future<Unit> addAccountTree(AccountTreeModel accountsTreeModel) async {
    const endPoint = ApiEndPoints.accountsTrees;
    try {
      await _apiService.postResponse(
        endPoint,
        data: accountsTreeModel.toJson(),
      );

      log('AccountsTreeRemoteDataSourceImplUrl: $endPoint');
      return unit;
    } catch (error) {
      log('AccountsTreeRemoteDataSourceImplError: $error');
      rethrow;
    }
  }

  @override
  Future<Unit> updateAccountTree(AccountTreeModel accountsTreeModel) async {
    final endPoint = '${ApiEndPoints.accountsTrees}/${accountsTreeModel.id}';
    try {
      await _apiService.putResponse(
        endPoint,
        data: accountsTreeModel.toJson(),
      );

      log('AccountsTreeRemoteDataSourceImplUrl: $endPoint');
      return unit;
    } catch (error) {
      log('AccountsTreeRemoteDataSourceImplError: $error');
      rethrow;
    }
  }
}
