import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:optimum_app/src/core/adaptive_widgets/adaptive_top_bar.dart';
import 'package:optimum_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:optimum_app/src/core/utils/nav.dart';

import '../add_cheque/add_cheque_page.dart';
import 'widgets/cheques_table.dart';

class ChequesPage extends HookWidget {
  static const routeName = '/accounting/cheques';
  const ChequesPage({super.key});

  @override
  Widget build(BuildContext context) {
    final local = AppLocalizations.of(context)!;

    return Scaffold(
        appBar: AdaptiveTopBar(
          title: local.cheques,
          actions: [
            BaseAddCircleButton(
                onPressed: () =>
                    NV.nextScreenNamed(context, AddChequePage.routeName)),
          ],
        ),
        body: const ChequesTable());
  }
}
