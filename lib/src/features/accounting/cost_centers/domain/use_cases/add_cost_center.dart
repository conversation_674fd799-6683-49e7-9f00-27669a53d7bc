import 'package:dartz/dartz.dart';
import 'package:optimum_app/src/core/consts/error/failures.dart';

import '../entities/cost_centers_entity.dart';
import '../repositories/cost_centers_repository.dart';

class AddCostCenterUseCase {
  final CostCentersRepository repository;

  AddCostCenterUseCase(this.repository);

  Future<Either<Failure, Unit>> call(CostCenterEntity costCenterEntity) async {
    return await repository.addCostCenter(
      costCenterEntity,
    );
  }
}
