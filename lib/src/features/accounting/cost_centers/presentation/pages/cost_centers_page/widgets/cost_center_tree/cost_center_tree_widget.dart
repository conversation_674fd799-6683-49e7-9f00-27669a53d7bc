import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:optimum_app/src/core/consts/strings/consts.dart';
import 'package:optimum_app/src/core/packages/tree_view/flutter_tree.dart';

import '../../../../../domain/entities/cost_centers_entity.dart';
import '../add_cost_center/cost_centers_bottom_sheet.dart';
import 'widgets.dart';

class CostCenterTreeWidget extends HookWidget {
  final List<CostCenterEntity> costCenters;
  final ValueNotifier<CostCenterEntity?> selectedNode;
  final int cosCenterLevelDepth;

  const CostCenterTreeWidget(
      {super.key,
      required this.costCenters,
      required this.selectedNode,
      required this.cosCenterLevelDepth});

  @override
  Widget build(BuildContext context) {
    List<TreeNodeData> getDynamicChildren(CostCenterEntity costCenter) {
      List<TreeNodeData> dynamicChildren = [];
      for (int i = 0; i < (costCenter.children?.length ?? 0); i++) {
        final child = costCenter.children![i];

        if (child.children!.isNotEmpty) {
          dynamicChildren
              .add(treeNodeData(child, children: getDynamicChildren(child)));
        } else {
          dynamicChildren.add(treeNodeData(child, isLast: true));
        }
      }
      return dynamicChildren;
    }

    log('asdasdwafasf ${costCenters.map((e) => e.children?.length)}');

    final data = costCenters.map((costCenter1) {
      return treeNodeData(costCenter1,
          children: getDynamicChildren(costCenter1));
    }).toList();

    Future<List<TreeNodeData>> load(TreeNodeData parent) async {
      await Future.delayed(const Duration(seconds: 1));

      return data;
    }

    void onTapNode(TreeNodeData node, {bool isAdd = false}) {
      selectedNode.value = node.extra;

      final selectedCostCenter = selectedNode.value;

      late Map<String, ValueNotifier> valueNotifiers;

      if (isAdd) {
        valueNotifiers = {
          Consts.mainCenter:
              ValueNotifier(selectedCostCenter?.mainCenter?.name),
          Consts.code: ValueNotifier(''),
          Consts.name: ValueNotifier(''),
          Consts.costCenterLevelsDepth: ValueNotifier(''),
          Consts.notes: ValueNotifier(''),
          Consts.hasFinalCenters: ValueNotifier(0),
        };
      } else {
        valueNotifiers = {
          Consts.mainCenter:
              ValueNotifier(selectedCostCenter?.mainCenter?.name),
          Consts.code: ValueNotifier(selectedCostCenter?.code),
          Consts.name: ValueNotifier(selectedCostCenter?.name),
          Consts.costCenterLevelsDepth:
              ValueNotifier(selectedCostCenter?.level?.toString()),
          Consts.notes: ValueNotifier(selectedCostCenter?.notes),
          Consts.hasFinalCenters:
              ValueNotifier(selectedCostCenter?.hasFinalCenters ?? 0),
        };
      }

      showBottomSheet(
          context: context,
          builder: (_) => SizedBox(
                height: 400,
                child: CostCentersBottomSheet(
                  costCenter: selectedNode.value,
                  valueNotifiers: valueNotifiers,
                  isAdd: isAdd,
                  cosCenterLevelDepth: cosCenterLevelDepth,
                ),
              ));
    }

    return TreeView(
      showActions: true,
      showFilter: true,
      load: load,
      onTap: (node) {
        onTapNode(node);
      },
      data: data,
      onLoad: (node) {
        log('onLoad');
      },
      onAppend: (node, parent) {
        final costCenter = node.extra as CostCenterEntity?;

        onTapNode(node, isAdd: costCenter?.isend == false);
      },
      onCheck: (checked, node) {
        log('checked');
        log('onCheck');
      },
      onCollapse: (node) {
        log('onCollapse');
      },
      onExpand: (node) {
        log('onExpand');
      },
      onRemove: (node, parent) {
        log('onRemove');
      },
    );
  }
}
