part of '../../authentication.dart';

abstract class AuthRemoteSource {
  Future<AccountModel> login(
      {required String email,
      required String password,
      required String otherDevices,
      required String deviceName});

  Future<Object> logout(String token);

  Future<List<String>> getValidateSubDomains({required String subdomain});

  Future<Object> forgotPassword(String email);

  Future<Object> resetPassword(
      {required String email,
      required String password,
      required String passwordConfirmation,
      required String token});
}

class AuthRemoteSourceImpl implements AuthRemoteSource {
  final http.Client client;

  AuthRemoteSourceImpl(this.client);

  @override
  Future<AccountModel> login(
      {required String email,
      required String password,
      required String otherDevices,
      required String deviceName}) async {
    final response = await client.post(Uri.parse(AuthURLs.login), headers: {
      "Accept": "application/json",
    }, body: {
      'code': email,
      'password': password,
      // 'other_devices': otherDevices,
      // 'device_name': deviceName,
      // 'email': email,
      // 'password': password,
      // 'other_devices': otherDevices,
      // 'device_name': deviceName,
    });

    final body = jsonDecode(response.body);

    Log.e('DDD ${Uri.parse(AuthURLs.login)} DDDDD ${body}');

    if (response.statusCode == 200) {
      return AccountModel.fromJson(body);
    } else {
      throw ServerException();
    }
  }

  @override
  Future<List<String>> getValidateSubDomains({
    required String subdomain,
  }) async {
    final response = await client
        .get(Uri.parse(AuthURLs.validateSubDomains(subdomain)), headers: {
      "Accept": "application/json",
      "Content-Type": "application/json",
    });

    log('aafssfwUUU ${AuthURLs.validateSubDomains(subdomain)}\n Ressss ${response.body}');

    final body = jsonDecode(response.body);
    if (response.statusCode == 200) {
      if (body['tenant'] == null) return [];

      final domainsList = body['tenant']['domains'];

      log('DOMAAAINS $domainsList');

      final domainsStringList = domainsList.map((e) {
        //? e = dev.opti4it.com for example
        final domain = e['domain'];

        if (domain == null || domain.toString().isEmpty) return '';

        final subDomain = domain.split('.');

        return subDomain[0];
      }).toList();

      return List<String>.from(domainsStringList);
    } else {
      throw ServerException();
    }
  }

  @override
  Future<Map> forgotPassword(String email) async {
    final response =
        await client.post(Uri.parse(AuthURLs.forgotPassword), headers: {
      "Accept": "application/json",
    }, body: {
      'email': email
    });

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw ServerException();
    }
  }

  @override
  Future<Map> logout(String token) async {
    final response = await client.post(Uri.parse(AuthURLs.logout), headers: {
      "Accept": "application/json",
      "Content-Type": "application/json",
      "Authorization": "Bearer $token"
    });

    if (response.statusCode == 200) {
      print(response.body);
      return jsonDecode(response.body);
    } else {
      throw ServerException();
    }
  }

  @override
  Future<Map> resetPassword(
      {required String email,
      required String password,
      required String passwordConfirmation,
      required String token}) async {
    final response = await client.post(Uri.parse(AuthURLs.login), headers: {
      "Accept": "application/json",
      "Content-Type": "application/json"
    }, body: {
      'email': email,
      'password': password,
      'passwordConfirmation': passwordConfirmation,
      'token': token,
    });

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw ServerException();
    }
  }
}
