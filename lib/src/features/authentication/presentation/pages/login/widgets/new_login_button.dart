// part of login;
//
// class _LoginButton extends StatelessWidget {
//   final TextEditingController emailController;
//   final TextEditingController passwordController;
//   final GlobalKey<FormState> formKey;
//
//   const _LoginButton({
//     required this.emailController,
//     required this.passwordController,
//     required this.formKey,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     final local = AppLocalizations.of(context)!;
//
//     return BlocBuilder<AuthenticationBloc, AuthenticationState>(
//       builder: (context, state) {
//         final isLoading = state is AuthenticationLoading;
//
//         if (isLoading) {
//           return const Center(
//             child: LoadingWidget(),
//           );
//         }
//
//         return BaseButton(
//           label: local.login,
//           color: Colors.white,
//           isWhiteText: false,
//           // isLoading: isLoading,
//           onPressed: () {
//             if (!formKey.currentState!.validate()) return;
//
//             final email = emailController.text.trim();
//             final password = passwordController.text.trim();
//
//             if (email.isEmpty) {
//               ScaffoldMessenger.of(context).showSnackBar(
//                 SnackBar(content: Text(local.pleaseEnterEmail)),
//               );
//               return;
//             }
//
//             if (password.isEmpty) {
//               ScaffoldMessenger.of(context).showSnackBar(
//                 SnackBar(content: Text(local.pleaseEnterPassword)),
//               );
//               return;
//             }
//
//             context.read<AuthenticationBloc>().add(LoginEvent(
//                   email: email,
//                   password: password,
//                   deviceName: 'postman',
//                   otherDevices: '0',
//                 ));
//           },
//         );
//       },
//     );
//   }
// }
