import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:optimum_app/src/core/consts/assets/assets.dart';
import 'package:optimum_app/src/core/consts/spaces/spaces.dart';
import 'package:optimum_app/src/core/consts/theme/theme.dart';
import 'package:optimum_app/src/core/extensions/extensions.dart';
import 'package:optimum_app/src/core/shared_widgets/animated/entrance_fader.dart';
import 'package:optimum_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:optimum_app/src/features/authentication/presentation/bloc/authentication_bloc.dart';

import '../../../../../core/shared_widgets/login_loading.dart';
import '../../../../../core/utils/injector/injector.dart';
import '../../../../settings/appearance_settings/components/settings_controller.dart';

part 'widgets/login_form.dart';
// part 'widgets/reset_form.dart';

//? Settings screen

class LoginScreen extends HookWidget {
  static const routeName = "/login";

  const LoginScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final settingsController = sl<SettingsController>();

    // Company is now handled directly in the login form

    return BlocBuilder<AuthenticationBloc, AuthenticationState>(
      builder: (context, state) {
        return Stack(
          children: [
            Scaffold(
              body: SafeArea(
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 8),
                              decoration: BoxDecoration(
                                border: Border.all(
                                  color: ColorManager.primaryColor,
                                ),
                                borderRadius: BorderRadius.circular(7),
                              ),
                              child: GestureDetector(
                                onTap: () {
                                  settingsController.updateLanguage(
                                      Locale(context.isEng ? 'ar' : 'en'));
                                },
                                child: Row(
                                  children: [
                                    const Icon(CupertinoIcons.globe, size: 26),
                                    context.smallGap,
                                    Text(
                                      context.isEng ? 'En' : 'عربي',
                                      style:
                                          theme.textTheme.titleMedium?.copyWith(
                                        fontWeight: FontWeight.w900,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            EntranceFader(
                              child: Hero(
                                tag: "AppLogo",
                                child: Image.asset(
                                  ImagesAssets.appLogo,
                                  width: 80,
                                  fit: BoxFit.cover,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      context.xxLargeGap,
                      BlocBuilder<AuthenticationBloc, AuthenticationState>(
                        builder: (context, state) {
                          return Center(
                            child: state is AuthenticationFailed
                                ? Text(state.message)
                                : null,
                          );
                        },
                      ),
                      const EntranceFader(
                          offset: Offset(0, 50), child: LoginForm()),
                    ],
                  ),
                ),
              ),
            ),
            // Show loading overlay when authenticating
            if (state is AuthenticationLoading) const LoginLoadingScreen(),
          ],
        );
      },
    );
    // return Scaffold(
    //   body: SafeArea(
    //     child: SingleChildScrollView(
    //       child: Column(
    //         mainAxisAlignment: MainAxisAlignment.start,
    //         crossAxisAlignment: CrossAxisAlignment.start,
    //         children: [
    //           Padding(
    //             padding: const EdgeInsets.all(16.0),
    //             child: Row(
    //               mainAxisAlignment: MainAxisAlignment.spaceBetween,
    //               children: [
    //                 Container(
    //                   padding: const EdgeInsets.symmetric(
    //                       horizontal: 12, vertical: 8),
    //                   decoration: BoxDecoration(
    //                     border: Border.all(
    //                       color: ColorManager.primaryColor,
    //                     ),
    //                     borderRadius: BorderRadius.circular(7),
    //                   ),
    //                   child: GestureDetector(
    //                     onTap: () {
    //                       // showChangeLanguageDialog(context);
    //                       settingsController.updateLanguage(
    //                           Locale(context.isEng ? 'ar' : 'en'));
    //                     },
    //                     child: Row(
    //                       children: [
    //                         const Icon(CupertinoIcons.globe, size: 26),
    //                         context.smallGap,
    //                         Text(
    //                           context.isEng ? 'En' : 'عربي',
    //                           style: theme.textTheme.titleMedium?.copyWith(
    //                             fontWeight: FontWeight.w900,
    //                           ),
    //                         ),
    //                       ],
    //                     ),
    //                   ),
    //                 ),
    //                 EntranceFader(
    //                   child: Hero(
    //                     tag: "AppLogo",
    //                     child: Image.asset(
    //                       ImagesAssets.appLogo,
    //                       width: 80,
    //                       fit: BoxFit.cover,
    //                     ),
    //                   ),
    //                 ),
    //               ],
    //             ),
    //           ),
    //
    //           context.xxLargeGap,
    //
    //           BlocBuilder<AuthenticationBloc, AuthenticationState>(
    //             builder: (context, state) {
    //               return Center(
    //                 child: state is AuthenticationFailed
    //                     ? Text(state.message)
    //                     : null,
    //               );
    //             },
    //           ),
    //
    //           //? Login Form
    //           const EntranceFader(offset: Offset(0, 50), child: LoginForm()),
    //
    //           // context.xLargeGap,
    //           //
    //           // Center(
    //           //   child: EntranceFader(
    //           //     child: Text(
    //           //       txt.appTitle.toUpperCase(),
    //           //       style: textTheme(context).headlineLarge!.copyWith(
    //           //             fontWeight: FontWeight.w900,
    //           //             shadows: isIOS
    //           //                 ? null
    //           //                 : const [Shadow(offset: Offset(2, 2))],
    //           //             wordSpacing: 2,
    //           //             letterSpacing: 4,
    //           //             fontStyle: FontStyle.italic,
    //           //             foreground: Paint()
    //           //               ..style = PaintingStyle.stroke
    //           //               ..strokeWidth = 3.5
    //           //               ..color = ColorManager.primaryColor,
    //           //           ),
    //           //     ),
    //           //   ),
    //           // ),
    //
    //           // context.mediumGap,
    //           //
    //           // //? Forgot Password Button
    //           // EntranceFader(
    //           //   offset: const Offset(0, 50),
    //           //   child: Center(
    //           //     child: TextButton(
    //           //       onPressed: () =>
    //           //           forgotPassword.value = !forgotPassword.value,
    //           //       child: Text(
    //           //           forgotPassword.value
    //           //               ? txt.alreadyHaveAnAccount
    //           //               : txt.forgotPassword,
    //           //           style: context.labelLargeStyle.copyWith(
    //           //             fontWeight: FontWeight.w900,
    //           //             decoration: TextDecoration.underline,
    //           //           )),
    //           //     ),
    //           //   ),
    //           // ),
    //         ],
    //       ),
    //     ),
    //   ),
    // );
  }
}
