part of '../login_screen.dart';

class LoginForm extends HookWidget {
  const LoginForm({super.key});

  @override
  Widget build(BuildContext context) {
    final local = AppLocalizations.of(context)!;

    final companyController = useTextEditingController(
      text: AuthenticationBloc.selectedSubDomain.value?.isEmpty ?? true
          ? kDebugMode
              ? ''
              : ''
          : AuthenticationBloc.selectedSubDomain.value,
    );
    final emailController = useTextEditingController(text: kDebugMode ? '1' : ''
        // text: kDebugMode ? '<EMAIL>':''
        );
    final passwordController =
        useTextEditingController(text: kDebugMode ? 'Aa@#123456' : '');
    // useTextEditingController(text: kDebugMode ? 'opti@123\$' : '');

    final formKey = useState(GlobalKey<FormState>());

    return Form(
      key: formKey.value,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: AppSpaces.medium),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          // welcom text
          Text(
            context.isEng ? 'Welcome,' : 'مرحبًا بك،',
            style: context.titleStyle.copyWith(
              fontSize: 40,
              fontWeight: FontWeight.w900,
            ),
          ),

          context.xxLargeGap,

          //? Company Field
          BaseTextField(
            controller: companyController,
            hintText: local.company,
            icon: const Icon(
              Icons.business_outlined,
              size: 20,
            ),
            onChanged: (value) {
              AuthenticationBloc.selectedSubDomain.value = value;
            },
          ),

          context.fieldSpace,

          //? Email Field
          BaseTextField(
            controller: emailController,
            hintText: local.code,
            icon: const Icon(
              CupertinoIcons.person,
              size: 20,
            ),
          ),

          context.fieldSpace,

          //? Password Field
          BaseTextField(
            isObscure: true,
            controller: passwordController,
            hintText: local.password,
            icon: const Icon(
              CupertinoIcons.lock,
              size: 20,
            ),
          ),

          context.xxLargeGap,

          //? Login Button
          BlocBuilder<AuthenticationBloc, AuthenticationState>(
            builder: (context, state) {
              // final isLoading = state is AuthenticationLoading;

              // if (isLoading) {
              //   return const LoadingWidget();
              // }

              return BaseButton(
                  label: local.login,
                  onPressed: () {
                    if (!formKey.value.currentState!.validate()) return;

                    // Use the combined login with company event
                    context.read<AuthenticationBloc>().add(
                        LoginWithCompanyEvent(
                            company: companyController.text,
                            email: emailController.text,
                            password: passwordController.text,
                            deviceName: 'postman',
                            otherDevices: '0'));
                  });
            },
          ),
        ]),
      ),
    );
  }
}
