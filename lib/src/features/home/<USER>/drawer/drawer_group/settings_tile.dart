import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:optimum_app/src/core/consts/assets/assets.dart';
import 'package:optimum_app/src/core/local_models/drawer_element.dart';
import 'package:optimum_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:optimum_app/src/features/settings/appearance_settings/appearance_settings.dart';

import '../drawer_list_item.dart';

class SettingsTile extends StatelessWidget {
  const SettingsTile({super.key});

  @override
  Widget build(BuildContext context) {
    final local = AppLocalizations.of(context)!;

    return DrawerListItem(
      element: DrawerElement(
          title: local.settings,
          icon: const BaseLottieIcon(
            AnimatedAssets.settings,
            height: 35,
          )),
      subElements: [
        //! Public Settings
        // DrawerElement(
        //     title: local.publicSettings,
        //     icon: const BaseLottieIcon(
        //       AnimatedAssets.mainDetails,
        //     ),
        //     route: PublicSettingsPage.routeName),
        //
        // //! Accounting
        // DrawerElement(
        //     title: local.accounting,
        //     icon: const BaseLottieIcon(
        //       AnimatedAssets.accounting,
        //     ),
        //     route: AccountingSettingsPage.routeName),
        //
        // //! Serial
        // DrawerElement(
        //     title: local.serial,
        //     icon: const BaseLottieIcon(
        //       AnimatedAssets.serialSettings,
        //     ),
        //     route: SerialSettings.routeName),
        //
        // //! Clearance
        // DrawerElement(
        //     title: local.clearance,
        //     icon: const BaseLottieIcon(
        //       AnimatedAssets.clearanceSettings,
        //     ),
        //     route: ClearanceSettingsPage.routeName),

        //! Appearance
        DrawerElement(
            title: local.appearance,
            icon: const BaseLottieIcon(
              AnimatedAssets.appSettings,
            ),
            route: AppearanceSettings.routeName),
      ],
    );
  }
}
