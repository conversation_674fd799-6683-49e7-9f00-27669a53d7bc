class GetAttendanceDetailsParams {
  final int? employeeId;
  final String? startDate;
  final String? endDate;

  GetAttendanceDetailsParams({
    this.employeeId,
    this.startDate,
    this.endDate,
  });

  String toQuery() {
    final query = StringBuffer();
    if (employeeId != null) {
      query.write('employee_id=$employeeId&');
    }
    if (startDate != null) {
      query.write('start_date=$startDate&');
    }
    if (endDate != null) {
      query.write('end_date=$endDate');
    }
    return query.toString();
  }
}
