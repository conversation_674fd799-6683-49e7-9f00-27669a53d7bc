part of attendance_details_page;

class _AttendanceDetailsButton extends StatelessWidget {
  final Map<String, ValueNotifier> valueNotifiers;
  final ValueNotifier<bool> showResult;

  const _AttendanceDetailsButton({
    Key? key,
    required this.valueNotifiers,
    required this.showResult,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AttendanceDetailsCubit, AttendanceDetailsState>(
      builder: (context, state) {
        final isLoading =
            state is LoadingAddUpdateDeleteAttendanceDetailsState ||
                state is LoadingAttendanceDetailsState;

        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isLoading)
              const LoadingWidget(
                isLinear: true,
              ),
            Material(
                elevation: 16,
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: AppSpaces.xxLarge, vertical: AppSpaces.large),
                  child: Row(
                    children: [
                      Expanded(
                        child: _EditButton(
                            valueNotifiers: valueNotifiers,
                            showResult: showResult),
                      ),
                      if (showResult.value) ...[
                        context.mediumGap,
                        Expanded(
                          flex: 2,
                          child: _ApproveButton(
                              valueNotifiers: valueNotifiers,
                              showResult: showResult),
                        ),
                      ]
                    ],
                  ),
                )),
          ],
        );
      },
    );
  }
}

class _EditButton extends StatelessWidget {
  final Map<String, ValueNotifier> valueNotifiers;
  final ValueNotifier<bool> showResult;

  const _EditButton(
      {super.key, required this.valueNotifiers, required this.showResult});

  @override
  Widget build(BuildContext context) {
    void onEditPressed() {
      if (!showResult.value &&
          !(AttendanceDetailsCubit.formKey.currentState!.validate())) return;

      if (showResult.value) {
        showResult.value = false;
      } else {
        final startDate = valueNotifiers[Consts.startDate]!.value as DateTime?;
        final endDate = valueNotifiers[Consts.endDate]!.value as DateTime?;

        final params = GetAttendanceDetailsParams(
          employeeId: valueNotifiers[Consts.selectedEmployee]!.value!.id,
          startDate: startDate.formatDateTimeToApi,
          endDate: endDate.formatDateTimeToApi,
        );

        context
            .read<AttendanceDetailsCubit>()
            .getAllAttendanceDetails(params: params);
      }
    }

    return BaseButton(
        label: showResult.value ? context.local.edit : context.local.submit,
        icon: BaseLottieIcon(
          showResult.value ? AnimatedAssets.edit : AnimatedAssets.add,
          repeat: showResult.value ? false : true,
          height: showResult.value ? 27 : 45,
        ),
        isPrefixIcon: true,
        onPressed: onEditPressed);
  }
}

class _ApproveButton extends StatelessWidget {
  final Map<String, ValueNotifier> valueNotifiers;
  final ValueNotifier<bool> showResult;

  const _ApproveButton(
      {required this.valueNotifiers, required this.showResult});

  @override
  Widget build(BuildContext context) {
    void onApprovePressed() {
      final startDate = valueNotifiers[Consts.startDate]!.value as DateTime?;
      final endDate = valueNotifiers[Consts.endDate]!.value as DateTime?;

      final signatures = AttendanceDetailsCubit.get(context)
          .convertAttendanceDetailsTableToEntity();

      final params = ApproveAttendanceDetailsParams(
          employeeId: valueNotifiers[Consts.selectedEmployee]!.value!.id,
          startDate: startDate.formatDateTimeToApi,
          endDate: endDate.formatDateTimeToApi,
          signatures: signatures);

      context.read<AttendanceDetailsCubit>().approveAttendanceDetails(
            context.local,
            approveAttendanceDetailsParams: params,
          );
    }

    return BaseButton(
        label: context.local.approve,
        color: ColorManager.primaryColor,
        icon: BaseLottieIcon(
          AnimatedAssets.add,
          repeat: showResult.value ? false : true,
          height: 45,
        ),
        isPrefixIcon: true,
        onPressed: onApprovePressed);
  }
}
