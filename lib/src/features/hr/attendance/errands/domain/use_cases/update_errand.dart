import 'package:dartz/dartz.dart';
import 'package:optimum_app/src/core/consts/error/failures.dart';
import 'package:optimum_app/src/features/hr/attendance/errands/domain/repositories/errands_repository.dart';

import '../entities/errands_entity.dart';

class UpdateErrandUseCase {
  final ErrandsRepository repository;

  UpdateErrandUseCase(this.repository);

  Future<Either<Failure, Unit>> call(
    ErrandEntity errand,
  ) async {
    return await repository.updateErrand(errand);
  }
}
