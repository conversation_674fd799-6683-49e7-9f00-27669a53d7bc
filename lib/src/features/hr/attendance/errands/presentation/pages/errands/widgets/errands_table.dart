part of '../errands_page.dart';

class _ErrandsTable extends StatelessWidget {
  const _ErrandsTable({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final local = AppLocalizations.of(context)!;

    List<String> titles = [
      local.code,
      local.admin,
      local.startDate,
      local.endDate,
      local.notes,
      local.actions
    ];

    List<Widget> cells({
      required ErrandEntity errand,
    }) {
      return [
        CellWidget(errand.id.toString()),
        CellWidget(errand.admin?.name ?? ''),
        CellWidget(errand.startDate?.toString() ?? ''),
        CellWidget(errand.endDate?.toString() ?? ''),
        CellWidget(errand.notes ?? ''),
        ActionButtons(
            onEdit: () => NV.nextScreen(context, AddErrandPage(errand: errand)),
            onDelete: () => showPlatformDialog(
                  context,
                  isDelete: true,
                  title: local.deleteErrand,
                  content: local.deleteErrandMessage,
                  action: () => context
                      .read<ErrandsCubit>()
                      .deleteErrand(local, id: errand.id!),
                )),
      ];
    }

    return _Table(
      titles: titles,
      cells: cells,
    );
  }
}

class _Table extends HookWidget {
  final List<String> titles;
  final List<Widget> Function({required ErrandEntity errand}) cells;

  const _Table({Key? key, required this.titles, required this.cells})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ErrandsCubit, ErrandsState>(
        builder: (context, errandsState) {
      if (errandsState is ErrorErrandsState) {
        return Center(
          child: Text(errandsState.message.translateErrorMessage),
        );
      }

      final showFooter =
          errandsState is LoadedErrandsState && errandsState.errands.isNotEmpty;

      //! Titles Row
      final columnWidget = titles
          .map(
            (e) => CellWidget(e, isTitle: true),
          )
          .toList(growable: false);

      //! Data Rows
      List<DataRow> rowWidget() {
        if (errandsState is LoadingErrandsState ||
            errandsState is LoadingAddUpdateDeleteErrandState) {
          return LoadingWidget.loadingTable(titles.length);
        } else if (errandsState is LoadedErrandsState) {
          return errandsState.errands
              .map((e) => DataRow(
                    cells: cells(errand: e).map((e) => DataCell(e)).toList(),
                  ))
              .toList(growable: false);
        }
        return [];
      }

      //! Table Body
      Widget tableBody() {
        void onPerPageChanged(value) {
          ErrandsCubit.get(context).selectedPerPageEntries.value = value;
          ErrandsCubit.get(context).getAllErrands();
        }

        return CustomTable(
          selectedPerPageEntriesValue:
              ErrandsCubit.get(context).selectedPerPageEntries,
          onChangePerPage: onPerPageChanged,
          showFooter: showFooter,
          columns: columnWidget,
          rows: [
            //? Search Fields Row
            DataRow(
              selected: true,
              cells: titles
                  .map((e) => DataCell(
                        _SearchRow(title: e),
                      ))
                  .toList(growable: false),
            ),

            //? Data Rows
            ...rowWidget(),
          ],
        );
      }

      return Stack(
        children: [
          tableBody(),
          if (errandsState is LoadedErrandsState &&
              errandsState.errands.isEmpty)
            const Center(
              child: EmptyLottieIcon(),
            )
        ],
      );
    });
  }
}

class _SearchRow extends HookWidget {
  final String title;

  const _SearchRow({Key? key, required this.title}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final local = AppLocalizations.of(context)!;

    if (title != local.notes && title != local.actions) {
      return BaseTableTextField(
          hintText: title,
          onChanged: (value) => ErrandsCubit.get(context)
              .onSearchChanged(value, filterKeyParams: (title, local)));
    }

    return const SizedBox();
  }
}
