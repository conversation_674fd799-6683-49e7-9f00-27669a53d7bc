import 'package:dartz/dartz.dart';
import 'package:optimum_app/src/core/consts/error/failures.dart';

import '../entities/permission_requests_entity.dart';

abstract class PermissionRequestsRepository {
  Future<Either<Failure, List<PermissionRequestEntity>>> getPermissionRequests(
      {String? columnFilters, int? perPage});
  Future<Either<Failure, Unit>> addPermissionRequest(
      PermissionRequestEntity permissionRequest);
  Future<Either<Failure, Unit>> updatePermissionRequest(
      PermissionRequestEntity permissionRequest);
  Future<Either<Failure, Unit>> deletePermissionRequest(int id);
}
