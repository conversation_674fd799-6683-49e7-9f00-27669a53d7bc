import 'package:dartz/dartz.dart';
import 'package:optimum_app/src/core/consts/error/failures.dart';
import 'package:optimum_app/src/features/hr/attendance/permission_request/domain/repositories/permission_requests_repository.dart';

import '../entities/permission_requests_entity.dart';

class UpdatePermissionRequestUseCase {
  final PermissionRequestsRepository repository;

  UpdatePermissionRequestUseCase(this.repository);

  Future<Either<Failure, Unit>> call(
    PermissionRequestEntity permissionRequest,
  ) async {
    return await repository.updatePermissionRequest(permissionRequest);
  }
}
