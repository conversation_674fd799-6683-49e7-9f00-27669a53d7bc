part of '../permission_requests_page.dart';

class _PermissionRequestsTable extends StatelessWidget {
  const _PermissionRequestsTable({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final local = AppLocalizations.of(context)!;

    List<String> titles = [
      local.code,
      local.permissionType,
      local.employee,
      local.status,
      local.requestDate,
      local.startTime,
      local.endTime,
      local.notes,
      local.actions,
    ];

    List<Widget> cells({
      required PermissionRequestEntity permissionRequest,
    }) {
      final isApproved = permissionRequest.directManagerOpinion == 'Approved';
      return [
        CellWidget(permissionRequest.id.toString()),
        CellWidget(permissionRequest.permissionType?.name ?? ''),
        CellWidget(permissionRequest.employee?.name ?? ''),
        StatusWidget(
          text: permissionRequest.directManagerOpinion ?? '',
          color: isApproved ? ColorManager.green : ColorManager.orange,
        ),
        CellWidget(permissionRequest.requestDate ?? ''),
        CellWidget(permissionRequest.startTime?.toString() ?? ''),
        CellWidget(permissionRequest.endTime?.toString() ?? ''),
        CellWidget(permissionRequest.notes ?? ''),
        ActionButtons(
            onEdit: () => NV.nextScreen(context,
                AddPermissionRequestPage(permissionRequest: permissionRequest)),
            onDelete: () => showPlatformDialog(
                  context,
                  isDelete: true,
                  title: local.deletePermissionRequest,
                  content: local.deletePermissionRequestMessage,
                  action: () => context
                      .read<PermissionRequestsCubit>()
                      .deletePermissionRequest(local,
                          id: permissionRequest.id!),
                )),
      ];
    }

    return _Table(
      titles: titles,
      cells: cells,
    );
  }
}

class _Table extends HookWidget {
  final List<String> titles;
  final List<Widget> Function(
      {required PermissionRequestEntity permissionRequest}) cells;

  const _Table({Key? key, required this.titles, required this.cells})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<PermissionRequestsCubit, PermissionRequestsState>(
        builder: (context, permissionRequestsState) {
      if (permissionRequestsState is ErrorPermissionRequestsState) {
        return Center(
          child: Text(permissionRequestsState.message.translateErrorMessage),
        );
      }

      final showFooter =
          permissionRequestsState is LoadedPermissionRequestsState &&
              permissionRequestsState.permissionRequests.isNotEmpty;

      //! Titles Row
      final columnWidget = titles
          .map(
            (e) => CellWidget(e, isTitle: true),
          )
          .toList(growable: false);

      //! Data Rows
      List<DataRow> rowWidget() {
        if (permissionRequestsState is LoadingPermissionRequestsState ||
            permissionRequestsState
                is LoadingAddUpdateDeletePermissionRequestState) {
          return LoadingWidget.loadingTable(titles.length);
        } else if (permissionRequestsState is LoadedPermissionRequestsState) {
          return permissionRequestsState.permissionRequests
              .map((e) => DataRow(
                    cells: cells(permissionRequest: e)
                        .map((e) => DataCell(e))
                        .toList(),
                  ))
              .toList(growable: false);
        }
        return [];
      }

      //! Table Body
      Widget tableBody() {
        void onPerPageChanged(value) {
          PermissionRequestsCubit.get(context).selectedPerPageEntries.value =
              value;
          PermissionRequestsCubit.get(context).getAllPermissionRequests();
        }

        return CustomTable(
          selectedPerPageEntriesValue:
              PermissionRequestsCubit.get(context).selectedPerPageEntries,
          onChangePerPage: onPerPageChanged,
          showFooter: showFooter,
          columns: columnWidget,
          rows: [
            //? Search Fields Row
            DataRow(
              selected: true,
              cells: titles
                  .map((e) => DataCell(
                        _SearchRow(title: e),
                      ))
                  .toList(growable: false),
            ),

            //? Data Rows
            ...rowWidget(),
          ],
        );
      }

      return Stack(
        children: [
          tableBody(),
          if (permissionRequestsState is LoadedPermissionRequestsState &&
              permissionRequestsState.permissionRequests.isEmpty)
            const Center(
              child: EmptyLottieIcon(),
            )
        ],
      );
    });
  }
}

class _SearchRow extends HookWidget {
  final String title;

  const _SearchRow({Key? key, required this.title}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final local = AppLocalizations.of(context)!;

    if (title != local.notes && title != local.actions) {
      return BaseTableTextField(
          hintText: title,
          onChanged: (value) => PermissionRequestsCubit.get(context)
              .onSearchChanged(value, filterKeyParams: (title, local)));
    }

    return const SizedBox();
  }
}
