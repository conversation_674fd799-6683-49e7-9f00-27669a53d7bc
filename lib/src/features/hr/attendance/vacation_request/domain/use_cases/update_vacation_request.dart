import 'package:dartz/dartz.dart';
import 'package:optimum_app/src/core/consts/error/failures.dart';
import 'package:optimum_app/src/features/hr/attendance/vacation_request/domain/repositories/vacation_requests_repository.dart';

import '../entities/vacation_requests_entity.dart';

class UpdateVacationRequestUseCase {
  final VacationRequestsRepository repository;

  UpdateVacationRequestUseCase(this.repository);

  Future<Either<Failure, Unit>> call(
    VacationRequestEntity vacationRequest,
  ) async {
    return await repository.updateVacationRequest(vacationRequest);
  }
}
