//Imports
import 'dart:developer';

import 'package:dartz/dartz.dart' hide id, State;
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:optimum_app/src/core/consts/error/exceptions.dart';
import 'package:optimum_app/src/core/consts/error/failures.dart';
import 'package:optimum_app/src/core/data/remote/network/network_api_service.dart';
import 'package:optimum_app/src/core/utils/injector/injector.dart';
import 'package:optimum_app/src/features/hr/employees/employees_list/employees_imports.dart';
import 'package:optimum_app/src/features/hr/main_details/departments_and_sections/domain/entities/department_entity.dart';
import 'package:optimum_app/src/features/hr/main_details/departments_and_sections/presentation/cubit/cubit.dart';
import 'package:optimum_app/src/features/hr/main_details/jobs/domain/entities/jobs_entity.dart';
import 'package:optimum_app/src/features/hr/main_details/salary_items/data/models/salary_items_model.dart';
import 'package:optimum_app/src/features/hr/main_details/salary_items/domain/entities/salary_items_entity.dart';
import 'package:optimum_app/src/features/settings/general_settings/domain/entities/general_settings_entity.dart';

import '../../../../core/adaptive_widgets/adaptive_top_bar.dart';
import '../../../../core/consts/assets/assets.dart';
import '../../../../core/consts/spaces/spaces.dart';
import '../../../../core/consts/strings/consts.dart';
import '../../../../core/consts/theme/theme.dart';
import '../../../../core/data/remote/network/api_end_points.dart';
import '../../../../core/shared_widgets/dialogs/platform_dialog.dart';
import '../../../../core/shared_widgets/shared_widgets.dart';
import '../../../../core/shared_widgets/tables/add_custom_table.dart';
import '../../../../core/shared_widgets/tables/custom_table.dart';
import '../../../../core/utils/nav.dart';
import '../../../home/<USER>/drawer/app_drawer.dart';
import '../../main_details/jobs/data/models/jobs_model.dart';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:optimum_app/src/core/extensions/extensions.dart';

part 'data/models/bonus_page_data_model.dart';
part 'data/models/edit_bonus_page_data_model.dart';
part 'data/models/bonus_model.dart';
part 'data/repositories/bonuses_repository_impl.dart';
part 'data/sources/bonuses_remote_data_source.dart';
//Parts
part 'domain/entities/bonus_entity.dart';
part 'domain/entities/bonus_page_data_entity.dart';
part 'domain/entities/edit_bonus_page_data_entity.dart';
part 'domain/repositories/bonuses_repository.dart';
//use cases
part 'domain/use_cases/add_bonus_use_case.dart';
part 'domain/use_cases/delete_bonus_use_case.dart';
part 'domain/use_cases/edit_bonus_use_case.dart';
part 'domain/use_cases/get_bonus_page_data_use_case.dart';
part 'domain/use_cases/get_bonuses_use_case.dart';
part 'domain/use_cases/get_edit_bonus_page_data_use_case.dart';


// presentation
part 'presentation/cubit/bonuses_cubit.dart';
part 'presentation/cubit/bonuses_state.dart';
part 'presentation/pages/add_bonus/add_bonus_page.dart';
part 'presentation/pages/add_bonus/widgets/bonus_save_button.dart';
part 'presentation/pages/add_bonus/widgets/add_bonus_fields.dart';
part 'presentation/pages/add_bonus/widgets/fields/bonus_details_fields.dart';
part 'presentation/pages/add_bonus/salary_items/salary_items_page.dart';
part 'presentation/pages/add_bonus/salary_items/salary_items_table.dart';
part 'presentation/pages/bonuses_page/bonuses_page.dart';
part 'presentation/pages/bonuses_page/widgets/bonuses_table.dart';
