part of "../../cvs_imports.dart";

abstract class CVsRemoteSource {
  Future<List<CVModel>> getAllCVs({String? columnFilters, int? perPage});
  Future<CVPageDataModel> getCVPageData();
  Future<CVEditPageDataModel> getCVEditPageData(String id);
  Future< Unit> deleteCV(String id);
  Future< Unit> editCV(CVModel cv);
  Future<Unit> addCV(CVModel cv);

}

class CVsRemoteSourceImpl implements CVsRemoteSource {
  final NetworkApiService apiService;

  CVsRemoteSourceImpl(this.apiService);

  @override
  Future<List<CVModel>> getAllCVs(
      {String? columnFilters, int? perPage}) async {
    try {
      final response = await apiService.getResponse(
          '${HRModuleURLs.cvs}?columnFilters=${columnFilters ?? []}&sortField=id&sortType=Desc&perPage=$perPage&page=1');
      final cvs = compute(
        responseToCVs,
        response,
      );
      return cvs;
    } catch (error) {
      debugPrint('CVRemoteDataSourceImplError:$error');
      rethrow;
    }
  }

  @override
  Future<Unit> addCV(CVModel cv) async {
    try {
      final response = await apiService.postResponse(HRModuleURLs.cvs, data: cv.toJson());
      return unit;
     } catch (error) {
      debugPrint('CVRemoteDataSourceImplError: $error');
      rethrow;
    }
  }

  @override
  Future<Unit> deleteCV(String id) async {
    try {
      await apiService.deleteResponse(
        "${HRModuleURLs.cvs}/$id",
      );
      return unit;
    } catch (error) {
      debugPrint('CVRemoteDataSourceImplError: $error');
      rethrow;
    }
  }

  @override
  Future<Unit> editCV(CVModel cv) async {

      final response = await apiService.putResponse("${HRModuleURLs.cvs}/${cv.id}",
          data: cv.toEditJson());
      return unit;
      try {} catch (error) {
      debugPrint('CVRemoteDataSourceImplError: $error');
      rethrow;
    }
  }

  @override
  Future<CVPageDataModel> getCVPageData() async{
    try {
      final response = await apiService.getResponse(HRModuleURLs.cvsCreate);
      final cvPageData = compute(
        responseToCVPageDataModel,
        response,
      );
      return cvPageData;
    } catch (error) {
      debugPrint('CVRemoteDataSourceImplError:$error');
      throw Exception(error.toString());
      rethrow;
    }
  }

  @override
  Future<CVEditPageDataModel> getCVEditPageData(String id) async{
    try {
      final response = await apiService.getResponse(
         "${HRModuleURLs.cvs}/$id/edit");
      final cvEditPageData = compute(
        responseToCVEditPageDataModel,
        response,
      );
      return cvEditPageData;
       } catch (error) {
      debugPrint('CVRemoteDataSourceImplError:$error');
      rethrow;
    }
  }

}


