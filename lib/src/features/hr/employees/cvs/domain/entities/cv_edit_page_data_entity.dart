part of "../../cvs_imports.dart";

class CVEditPageDataEntity extends CVPageDataEntity {
  final CVEntity? cv;
  const CVEditPageDataEntity({
    this.cv,
    super.countries,
    super.departments,
    super.shifts,
    super.regulations,
    super.salaryItems,
    super.cvs,
    super.jobs,
  });
  @override
  List<Object?> get props => [
        countries,
        departments,
        shifts,
        regulations,
        salaryItems,
        cvs,
        jobs,
        cv,
      ];
}
