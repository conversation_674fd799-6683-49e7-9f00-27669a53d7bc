part of "../../cvs_imports.dart";


class CVEntity extends Equatable{

  final int? id;
  final String? nameEn;
  final String? nameAr;
  final String? status;
  final int? sectionId;
  final int? jobId;
  final String? avater;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? deletedAt;
  final int? departmentId;
  final int? branchId;
  final String? gender;
  final String? maritalStatus;
  final String? religion;
  final DateTime? dateOfBirth;
  final String? placeOfBirth;
  final MainContactsEntity? contacts;
  final MainIdentityEntity? identity;
  final List<MainQualificationEntity>? qualifications;
  final List<MainExperienceEntity>? experiences;
  final DateTime? dateOfHiring;
  final String? jobInResidence;
  final String? name;
  final DepartmentEntity? section;
  final JobEntity? job;
  final int? countryId;


  static CVModel toModel(CVEntity cv)=> CVModel(
  id:cv.id ,
  nameEn:cv.nameEn ,
  nameAr:cv.nameAr ,
  status:cv.status ,
  sectionId:cv.sectionId ,
  jobId:cv.jobId ,
  avater:cv.avater ,
  createdAt:cv.createdAt ,
  updatedAt:cv.updatedAt ,
  deletedAt:cv.deletedAt ,
  departmentId:cv.departmentId ,
  branchId:cv.branchId ,
  gender:cv.gender ,
  maritalStatus:cv.maritalStatus ,
  religion:cv.religion ,
  dateOfBirth:cv.dateOfBirth ,
  placeOfBirth:cv.placeOfBirth ,
  contacts:cv.contacts ,
  identity:cv.identity ,
  qualifications:cv.qualifications ,
  experiences:cv.experiences ,
  dateOfHiring:cv.dateOfHiring ,
  jobInResidence:cv.jobInResidence ,
  name:cv.name ,
  section:cv.section ,
  job:cv.job ,
    countryId:cv.countryId ,

);
  const CVEntity({
    this.id,
    this.nameEn,
    this.nameAr,
    this.status,
    this.sectionId,
    this.jobId,
    this.avater,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.departmentId,
    this.branchId,
    this.gender,
    this.maritalStatus,
    this.religion,
    this.dateOfBirth,
    this.placeOfBirth,
    this.contacts,
    this.identity,
    this.qualifications,
    this.experiences,
    this.dateOfHiring,
    this.jobInResidence,
    this.name,
    this.section,
    this.job,
    this.countryId,
  });

  @override
  List<Object?> get props => [
    id,
    nameEn,
    nameAr,
    status,
    sectionId,
    jobId,
    avater,
    createdAt,
    updatedAt,
    deletedAt,
    departmentId,
    branchId,
    gender,
    maritalStatus,
    religion,
    dateOfBirth,
    placeOfBirth,
    contacts,
    identity,
    qualifications,
    experiences,
    dateOfHiring,
    jobInResidence,
    name,
    section,
    job,
    countryId,
  ];

}
