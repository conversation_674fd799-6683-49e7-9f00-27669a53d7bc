import 'package:flutter/material.dart';
import 'package:optimum_app/src/core/extensions/extensions.dart';

class RowTextWidget extends StatelessWidget {
  final String label;
  final String value;
  const RowTextWidget({Key? key, required this.label, required this.value})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        children: [
          context.smallGap,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  label,
                  style: context.labelLargeStyle,
                ),
              ),
              Flexible(
                child: Text(
                  value,
                  style: context.labelMediumStyle,
                ),
              ),
            ],
          ),
          context.smallGap,
          const Divider(
            thickness: .2,
          ),
        ],
      ),
    );
  }
}
