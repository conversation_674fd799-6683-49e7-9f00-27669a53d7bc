part of "../../employees_imports.dart";

EmployeeEditPageDataModel responseToEmployeeEditPageDataModel(response) {
  final employeeEditPageData = EmployeeEditPageDataModel.fromJson(response['data']);
  return employeeEditPageData;
}

class EmployeeEditPageDataModel extends EmployeeEditPageDataEntity{
  const EmployeeEditPageDataModel({
    super.employee,
    super.countries,
    super.departments,
    super.shifts,
    super.regulations,
    super.salaryItems,
    super.employees,
    super.jobs,
  });

  factory EmployeeEditPageDataModel.fromJson(Map<String, dynamic> json) => EmployeeEditPageDataModel(
    countries: json["countries"] == null ? [] : List<CountriesDataModel>.from(json["countries"]!.map((x) => CountriesDataModel.fromMap(x))),
    departments: json["departments"] == null ? [] : List<DepartmentModel>.from(json["departments"]!.map((x) => DepartmentModel.fromJson(x))),
    shifts: json["shifts"] == null ? [] : List<MainEntity>.from(json["shifts"]!.map((x) => MainModel.fromJson(x))),
    regulations: json["regulations"] == null ? [] : List<MainRegulationModel>.from(json["regulations"]!.map((x) => MainRegulationModel.fromJson(x))),
    salaryItems: json["salaryItems"] == null ? [] : List<SalaryItemModel>.from(json["salaryItems"]!.map((x) => SalaryItemModel.fromJson(x))),
    employees: json["employees"] == null ? [] : List<EmployeeModel>.from(json["employees"]!.map((x) => EmployeeModel.fromJson(x))),
    jobs: json["jobs"] == null ? [] : List<JobModel>.from(json["jobs"]!.map((x) => JobModel.fromJson(x))),
    employee: json["employee"] == null ? null : EmployeeModel.fromJson(json["employee"]),
  );

  @override
  List<Object?> get props => [
    countries,
    departments,
    shifts,
    regulations,
    salaryItems,
    employees,
    jobs,
    employee,
  ];
}
