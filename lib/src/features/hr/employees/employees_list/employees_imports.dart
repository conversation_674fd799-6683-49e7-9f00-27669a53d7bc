//Imports
import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:dartz/dartz.dart' hide id, State;
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:optimum_app/src/core/shared_entities_and_models/employees/main_contacts_entity_and_model.dart';
import 'package:optimum_app/src/core/shared_entities_and_models/employees/main_experience_entity_and_model.dart';
import 'package:optimum_app/src/core/shared_entities_and_models/employees/main_identity_entity_and_model.dart';
import 'package:optimum_app/src/core/shared_entities_and_models/employees/main_qualification_entity_and_model.dart';
import 'package:optimum_app/src/core/utils/injector/injector.dart';
import 'package:optimum_app/src/features/hr/employees/bonuses/bonuses_imports.dart';
import 'package:optimum_app/src/features/hr/employees/employees_list/presentation/pages/widgets/tab_bar/employees_tab_bar.dart';
import 'package:optimum_app/src/features/hr/main_details/jobs/data/models/jobs_model.dart';
import 'package:optimum_app/src/features/hr/main_details/jobs/domain/entities/jobs_entity.dart';
import 'package:optimum_app/src/features/hr/regulations/data/models/regulations_model.dart';
import 'package:optimum_app/src/features/hr/regulations/domain/entities/regulations_entity.dart';

import '../../../../core/adaptive_widgets/adaptive_top_bar.dart';
import '../../../../core/consts/assets/assets.dart';
import '../../../../core/consts/error/exceptions.dart';
import '../../../../core/consts/error/failures.dart';
import '../../../../core/consts/spaces/spaces.dart';
import '../../../../core/consts/strings/consts.dart';
import '../../../../core/consts/theme/theme.dart';
import '../../../../core/data/remote/network/api_end_points.dart';
import '../../../../core/data/remote/network/network_api_service.dart';
import '../../../../core/extensions/extensions.dart';
import '../../../../core/shared_entities_and_models/employees/main_regulation/main_regulation_entity.dart';
import '../../../../core/shared_entities_and_models/employees/main_regulation/main_regulation_model.dart';
import '../../../../core/shared_entities_and_models/main_entity/main_entity.dart';
import '../../../../core/shared_entities_and_models/main_entity/main_model.dart';
import '../../../../core/shared_widgets/dialogs/platform_dialog.dart';
import '../../../../core/shared_widgets/shared_widgets.dart';
import '../../../../core/shared_widgets/tables/add_custom_table.dart';
import '../../../../core/shared_widgets/tables/custom_table.dart';
import '../../../../core/utils/nav.dart';
import '../../../home/<USER>/drawer/app_drawer.dart';
import '../../../management/countries_states/countries_states.dart';
import '../../../settings/general_settings/domain/entities/general_settings_entity.dart';
import '../../main_details/departments_and_sections/data/models/department_model.dart';
import '../../main_details/departments_and_sections/domain/entities/department_entity.dart';
import '../../main_details/salary_items/data/models/salary_items_model.dart';
import '../../main_details/salary_items/domain/entities/salary_items_entity.dart';

part 'data/data_sources/remote_data_source.dart';
part 'data/models/employee_edit_page_data_model.dart';
//Parts
// data layer
part 'data/models/employee_model.dart';
part 'data/models/employee_page_data_model.dart';
part 'data/repositories/repository_impl.dart';
part 'domain/entities/employee_edit_page_data_entity.dart';
// domain layer
part 'domain/entities/employee_entity.dart';
part 'domain/entities/employee_page_data_entity.dart';
part 'domain/repositories/repository.dart';
part 'domain/use_cases/add_employee_use_case.dart';
part 'domain/use_cases/delete_employee_use_case.dart';
part 'domain/use_cases/edit_employee_use_case.dart';
part 'domain/use_cases/get_bonus_employees_usecase.dart';
part 'domain/use_cases/get_employee_edit_page_data_use_case.dart';
part 'domain/use_cases/get_employee_page_data_use_case.dart';
part 'domain/use_cases/get_employees_usecase.dart';
// presentation layer
part 'presentation/cubit/employees_cubit.dart';
part 'presentation/cubit/employees_state.dart';
part 'presentation/pages/add_employee/add_employee_page.dart';
part 'presentation/pages/add_employee/add_employee_save_button.dart';
part 'presentation/pages/add_employee/contacts/contacts_fields.dart';
part 'presentation/pages/add_employee/experiances/experiances_page.dart';
part 'presentation/pages/add_employee/experiances/experiances_table.dart';
part 'presentation/pages/add_employee/identity/identity_fields.dart';
part 'presentation/pages/add_employee/main_details/main_details_fields.dart';
part 'presentation/pages/add_employee/qualifications/qualifications_page.dart';
part 'presentation/pages/add_employee/qualifications/qualifications_table.dart';
part 'presentation/pages/add_employee/salary_items/salary_items_page.dart';
part 'presentation/pages/add_employee/salary_items/salary_items_table.dart';
part 'presentation/pages/add_employee/work/work_fields.dart';
part 'presentation/pages/employees/employees_page.dart';
part 'presentation/pages/employees/widgets/employees_table.dart';
