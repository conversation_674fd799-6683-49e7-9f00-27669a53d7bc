part of "../../employees_imports.dart";

abstract class EmployeesState extends Equatable {
  const EmployeesState();

  @override
  List<Object> get props => [];
}

class EmployeesInitialState extends EmployeesState {}

//? Get Employee States
class LoadingEmployeesState extends EmployeesState {}

class LoadedEmployeesState extends EmployeesState {
  final List<EmployeeEntity> employees;

  const LoadedEmployeesState({required this.employees});

  @override
  List<Object> get props => [employees];
}

//? Get Employee Page Data States
class LoadingEmployeesPageDataState extends EmployeesState {}
class LoadingEditEmployeesPageDataState extends EmployeesState {}

class LoadedEmployeesPageDataState extends EmployeesState {
  final EmployeePageDataEntity employeePageData;

  const LoadedEmployeesPageDataState({required this.employeePageData});

  @override
  List<Object> get props => [employeePageData];
}
class LoadedEditEmployeesPageDataState extends EmployeesState {
  final EmployeeEditPageDataEntity editEmployeePageData;

  const LoadedEditEmployeesPageDataState({required this.editEmployeePageData});

  @override
  List<Object> get props => [editEmployeePageData];
}

//? Add & Update & Delete Employee States
class LoadingAddUpdateDeleteEmployeeState extends EmployeesState {}

class SuccessEmployeesState extends EmployeesState {
  final String message;

  const SuccessEmployeesState({required this.message});

  @override
  List<Object> get props => [message];
}

//? Error Employee State
class ErrorEmployeesState extends EmployeesState {
  final String message;

  const ErrorEmployeesState({required this.message});

  @override
  List<Object> get props => [message];
}

