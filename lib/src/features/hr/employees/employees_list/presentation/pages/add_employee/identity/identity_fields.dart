part of "../../../../employees_imports.dart";

class _IdentityFields extends HookWidget {
  final Map<String, TextEditingController> controllers;
  final Map<String, ValueNotifier> valueNotifiers;

  const _IdentityFields({
    Key? key,
    required this.controllers,
    required this.valueNotifiers,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final local = AppLocalizations.of(context)!;

    final residencyNumberReleaseDate =
        valueNotifiers[Consts.residencyNumberReleaseDate]
            as ValueNotifier<DateTime?>;
    final licenseNumberReleaseDate =
        valueNotifiers[Consts.licenseNumberReleaseDate]
            as ValueNotifier<DateTime?>;
    final passportNumberReleaseDate =
        valueNotifiers[Consts.passportNumberReleaseDate]
            as ValueNotifier<DateTime?>;

    final residencyNumberExpiryDate =
        valueNotifiers[Consts.residencyNumberExpiryDate]
            as ValueNotifier<DateTime?>;
    final licenseNumberExpiryDate =
        valueNotifiers[Consts.licenseNumberExpiryDate]
            as ValueNotifier<DateTime?>;
    final passportNumberExpiryDate =
        valueNotifiers[Consts.passportNumberExpiryDate]
            as ValueNotifier<DateTime?>;

    return Column(
      children: [
        //! Entry number
        BaseTextField(
            isRequired: false,
            title: local.entryNumber,
            controller: controllers[Consts.entryNumber],
            hintText: local.entryNumber,
            icon: const BaseLottieFieldIcon(
              AnimatedAssets.userField,
            )),
        context.fieldSpace,

        const Divider(),
        Center(child: Text(local.residencyNumber),),
        const Divider(),

        //! residency Number Issuer
        BaseTextField(
            isRequired: false,
            title: local.issuer,
            controller: controllers[Consts.residencyNumberIssuer],
            hintText: local.issuer,
            icon: const BaseLottieFieldIcon(
              AnimatedAssets.userField,
            )),
        context.fieldSpace,
        //! residency Number Release Date
        ValueListenableBuilder(
          valueListenable: residencyNumberReleaseDate,
          builder: (context, value, child) {
            return BaseDatePicker(
                isRequired: false,
                onChanged: (date) {
                  residencyNumberReleaseDate.value = date!;
                },
                label: local.releaseDate,
                selectedDate: value);
          },
        ),
        context.fieldSpace,
        //! residency Number Expiry Date
        ValueListenableBuilder(
          valueListenable: residencyNumberExpiryDate,
          builder: (context, value, child) {
            return BaseDatePicker(
                isRequired: false,
                onChanged: (date) {
                  residencyNumberExpiryDate.value = date!;
                },
                label: local.expiryDate,
                selectedDate: value);
          },
        ),
        context.fieldSpace,

        const Divider(),
        Center(child: Text(local.licenseNumber),),
        const Divider(),


        //! license Number Issuer
        BaseTextField(
            isRequired: false,
            title: local.issuer,
            controller: controllers[Consts.licenseNumberIssuer],
            hintText: local.issuer,
            icon: const BaseLottieFieldIcon(
              AnimatedAssets.userField,
            )),
        context.fieldSpace,
        //! license Number Release Date
        ValueListenableBuilder(
          valueListenable: licenseNumberReleaseDate,
          builder: (context, value, child) {
            return BaseDatePicker(
                isRequired: false,
                onChanged: (date) {
                  licenseNumberReleaseDate.value = date!;
                },
                label: local.releaseDate,
                selectedDate: value);
          },
        ),
        context.fieldSpace,
        //! license Number Expiry Date
        ValueListenableBuilder(
          valueListenable: licenseNumberExpiryDate,
          builder: (context, value, child) {
            return BaseDatePicker(
                isRequired: false,
                onChanged: (date) {
                  licenseNumberExpiryDate.value = date!;
                },
                label: local.expiryDate,
                selectedDate: value);
          },
        ),

        context.fieldSpace,

        const Divider(),
        Center(child: Text(local.passportNumber),),
        const Divider(),

        //! passport Number Issuer
        BaseTextField(
            isRequired: false,
            title: local.issuer,
            controller: controllers[Consts.passportNumberIssuer],
            hintText: local.issuer,
            icon: const BaseLottieFieldIcon(
              AnimatedAssets.userField,
            )),
        context.fieldSpace,
        //! passport Number Release Date
        ValueListenableBuilder(
          valueListenable: passportNumberReleaseDate,
          builder: (context, value, child) {
            return BaseDatePicker(
                isRequired: false,
                onChanged: (date) {
                  passportNumberReleaseDate.value = date!;
                },
                label: local.releaseDate,
                selectedDate: value);
          },
        ),
        context.fieldSpace,
        //! passport Number Expiry Date
        ValueListenableBuilder(
          valueListenable: passportNumberExpiryDate,
          builder: (context, value, child) {
            return BaseDatePicker(
                isRequired: false,
                onChanged: (date) {
                  passportNumberExpiryDate.value = date!;
                },
                label: local.expiryDate,
                selectedDate: value);
          },
        ),
        context.fieldSpace,
      ],
    );
  }
}
