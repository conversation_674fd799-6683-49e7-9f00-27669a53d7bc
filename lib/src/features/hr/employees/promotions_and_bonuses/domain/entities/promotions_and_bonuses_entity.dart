import 'package:equatable/equatable.dart';
import 'package:optimum_app/src/features/hr/employees/employees_list/employees_imports.dart';
import 'package:optimum_app/src/features/hr/employees/promotions_and_bonuses/data/models/promotions_and_bonuses_model.dart';
import 'package:optimum_app/src/features/hr/main_details/jobs/domain/entities/jobs_entity.dart';
import 'package:optimum_app/src/features/hr/main_details/salary_items/domain/entities/salary_items_entity.dart';

class PromotionsAndBonusesEntity extends Equatable {
  final int? id;
  final int? adminId;
  final String? bonusDate;
  final String? createdAt;
  final EmployeeEntity? employee;
  final int? employeeId;
  final JobEntity? newJob;
  final int? newJobId;
  final String? notes;
  final JobEntity? oldJob;
  final int? oldJobId;

  final List<(int? salaryItemId, String? value, SalaryItemEntity? salaryItem)>?
      items;

  const PromotionsAndBonusesEntity(
      {this.id,
      this.adminId,
      this.bonusDate,
      this.createdAt,
      this.employee,
      this.employeeId,
      this.newJob,
      this.newJobId,
      this.notes,
      this.items,
      this.oldJob,
      this.oldJobId});

  static PromotionsAndBonusesModel toModel(
          PromotionsAndBonusesEntity promotionsAndBonusesModel) =>
      PromotionsAndBonusesModel(
        id: promotionsAndBonusesModel.id,
        adminId: promotionsAndBonusesModel.adminId,
        bonusDate: promotionsAndBonusesModel.bonusDate,
        createdAt: promotionsAndBonusesModel.createdAt,
        employee: promotionsAndBonusesModel.employee,
        employeeId: promotionsAndBonusesModel.employeeId,
        newJob: promotionsAndBonusesModel.newJob,
        newJobId: promotionsAndBonusesModel.newJobId,
        notes: promotionsAndBonusesModel.notes,
        oldJob: promotionsAndBonusesModel.oldJob,
        oldJobId: promotionsAndBonusesModel.oldJobId,
        items: promotionsAndBonusesModel.items,
      );

  @override
  List<Object?> get props => [
        id,
        adminId,
        bonusDate,
        createdAt,
        employee,
        employeeId,
        newJob,
        newJobId,
        notes,
        oldJob,
        oldJobId,
        items
      ];
}
