import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:optimum_app/src/core/consts/strings/consts.dart';
import 'package:optimum_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:optimum_app/src/core/shared_widgets/tables/add_custom_table.dart';
import 'package:optimum_app/src/features/hr/employees/promotions_and_bonuses/domain/entities/promotions_and_bonuses_entity.dart';
import 'package:optimum_app/src/features/hr/employees/promotions_and_bonuses/presentation/pages/add_promotions_and_bonuses/add_promotions_and_bonuses_page.dart';
import 'package:optimum_app/src/features/hr/main_details/salary_items/domain/entities/salary_items_entity.dart';

import '../../../../cubit/cubit.dart';

class AddPromotionsAndBonusesTable extends StatelessWidget {
  final PromotionsAndBonusesEntity? promotionsAndBonuses;
  final Function setState;

  const AddPromotionsAndBonusesTable({
    Key? key,
    required this.promotionsAndBonuses,
    required this.setState,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final local = AppLocalizations.of(context)!;

    List<String> titles = [
      local.salaryItem,
      local.value,
      local.actions,
    ];

    //? Cells ------------------------------
    List<Widget> cells(
        {required Map<String, ValueNotifier> tableFieldsControllers}) {
      return [
        //! Salary Items DropDown
        SalaryItemsDropDown(
          selectedSalaryItem: tableFieldsControllers[Consts.selectedSalaryItem]
              as ValueNotifier<SalaryItemEntity?>,
          fromAddTable: true,
        ),

        //! Value TextField
        BaseTableTextField(
            initialValue: tableFieldsControllers[Consts.value]!.value,
            hintText: local.value,
            onChanged: (val) {
              tableFieldsControllers[Consts.value]!.value = val;
            }),

        ActionButtons(
          onDelete: promotionsAndBonusesTableNotifiers.length > 1
              ? () {
                  promotionsAndBonusesTableNotifiers
                      .remove(tableFieldsControllers);
                  setState(() {});
                }
              : null,
        ),
      ];
    }

    //? Columns ------------------------------
    final columnWidget = titles
        .map(
          (e) => CellWidget(
            e,
            isTitle: true,
            isWhite: true,
          ),
        )
        .toList(growable: false);

    //? Rows ------------------------------
    List<DataRow> rowWidget() {
      return promotionsAndBonusesTableNotifiers
          .map((e) => DataRow(
                cells: cells(tableFieldsControllers: e)
                    .map((e) => DataCell(e))
                    .toList(),
              ))
          .toList(growable: false);
    }

    //? Table ------------------------------
    return BlocBuilder<PromotionsAndBonusesCubit, PromotionsAndBonusesState>(
      builder: (context, invoiceStates) {
        return AddCustomTable(
          columns: columnWidget,
          rows: rowWidget(),
        );
      },
    );
  }
}

class _BaseTimePicker extends StatelessWidget {
  final ValueNotifier<String?> valueNotifier;

  const _BaseTimePicker({super.key, required this.valueNotifier});

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: valueNotifier,
      builder: (context, value, child) {
        return BaseTableTextField(
          controller: TextEditingController(
              text: valueNotifier.value?.toString() ?? '--:--'),
          readOnly: true,
          onTap: () async {
            final time = await showTimePicker(
              context: context,
              initialTime: TimeOfDay.now(),
            );

            if (time != null) {
              valueNotifier.value = time.format(context);
            }
          },
        );
      },
    );
  }
}
