import 'package:dartz/dartz.dart';
import 'package:optimum_app/src/core/consts/error/failures.dart';

import '../entities/staff_status_entity.dart';

abstract class StaffStatusRepository {
  Future<Either<Failure, List<StaffStatusEntity>>> getStaffStatus(
      {String? columnFilters, int? perPage});
  Future<Either<Failure, Unit>> addStaffStatus(StaffStatusEntity staffStatus);
  Future<Either<Failure, Unit>> updateStaffStatus(
      StaffStatusEntity staffStatus);
  Future<Either<Failure, Unit>> deleteStaffStatus(int id);
}
