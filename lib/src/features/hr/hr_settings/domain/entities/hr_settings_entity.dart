part of '../../hr_settings.dart';

class HrSettingsEntity extends Equatable {
  final HrGeneralSettingsEntity? generalSettings;
  final AccountSettingsEntity? accountSettings;
  final HrGeneralSettingsEntity? hrGeneralSettings;

  const HrSettingsEntity({
    this.generalSettings,
    this.accountSettings,
    this.hrGeneralSettings,
  });

  static HrSettingsModel toModel(HrSettingsEntity hrSettings) =>
      HrSettingsModel(
        generalSettings: hrSettings.generalSettings,
        accountSettings: hrSettings.accountSettings,
        hrGeneralSettings: hrSettings.hrGeneralSettings,
      );

  @override
  List<Object?> get props => [
        generalSettings,
        accountSettings,
        hrGeneralSettings,
      ];
}
