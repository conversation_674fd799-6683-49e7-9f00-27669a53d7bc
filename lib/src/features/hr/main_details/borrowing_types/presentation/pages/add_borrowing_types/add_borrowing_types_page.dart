import 'dart:core';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:optimum_app/src/core/adaptive_widgets/adaptive_top_bar.dart';
import 'package:optimum_app/src/core/consts/assets/assets.dart';
import 'package:optimum_app/src/core/consts/spaces/spaces.dart';
import 'package:optimum_app/src/core/consts/strings/consts.dart';
import 'package:optimum_app/src/core/extensions/extensions.dart';
import 'package:optimum_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:optimum_app/src/core/utils/nav.dart';
import 'package:optimum_app/src/features/hr/main_details/salary_items/domain/entities/salary_items_entity.dart';
import 'package:optimum_app/src/features/hr/main_details/salary_items/presentation/cubit/cubit.dart';

import '../../../domain/entities/borrowing_types_entity.dart';
import '../../cubit/borrowing_types_cubit.dart';
import '../../cubit/cubit.dart';
import '../borrowing_types/borrowing_types_page.dart';

part 'widgets/add_borrowing_types_fields.dart';
part 'widgets/borrowing_types_save_button.dart';

class AddBorrowingTypePage extends HookWidget {
  final BorrowingTypeEntity? borrowingType;

  static const routeName = '/hr/borrowingTypes/add';

  const AddBorrowingTypePage({super.key, this.borrowingType});

  @override
  Widget build(BuildContext context) {
    final local = AppLocalizations.of(context)!;

    //? Add Controllers to Map to reduce parameters passing
    final Map<String, TextEditingController> controllers = {
      Consts.nameAr: useTextEditingController(text: borrowingType?.nameAr),
      Consts.nameEn: useTextEditingController(text: borrowingType?.nameEn),
      Consts.notes: useTextEditingController(text: borrowingType?.notes),
    };

    final selectedSalaryItem = useState<SalaryItemEntity?>(null);

    final isEdit = borrowingType != null;

    return WillPopScope(
      onWillPop: () async {
        NV.nextScreenCloseOthersNamed(context, BorrowingTypesPage.routeName);
        return false;
      },
      child: MultiBlocProvider(
        providers: [
          BlocProvider(
            create: (context) => BorrowingTypesCubit.getBorrowingTypesCubitSl(),
          ),
          BlocProvider(
            create: (context) =>
                SalaryItemsCubit.getSalaryItemsCubitSl()..getAllSalaryItems(),
          ),
        ],
        child: BlocListener<BorrowingTypesCubit, BorrowingTypesState>(
            listener: (context, state) {
              log('borrowingTypeState => ${state.runtimeType}');

              if (state is ErrorBorrowingTypesState) {
                Navigator.pop(context);
                NV.nextScreenNamed(context, BorrowingTypesPage.routeName);

                context.showBar(state.message.translateErrorMessage,
                    isError: true);
              } else if (state is SuccessBorrowingTypesState) {
                Navigator.pop(context);
                NV.nextScreenCloseOthersNamed(
                    context, BorrowingTypesPage.routeName);

                context.showBar(state.message);
              }
            },
            child: Scaffold(
              appBar: AdaptiveTopBar(
                title:
                    isEdit ? local.editBorrowingType : local.addBorrowingType,
              ),
              bottomNavigationBar: _BorrowingTypeSaveButton(
                controllers: controllers,
                borrowingType: borrowingType,
                selectedSalaryItem: selectedSalaryItem,
              ),
              body: Form(
                key: BorrowingTypesCubit.formKey,
                child: _AddBorrowingTypeFields(
                  controllers: controllers,
                  borrowingType: borrowingType,
                  selectedSalaryItem: selectedSalaryItem,
                ),
              ),
            )),
      ),
    );
  }
}
