part of '../borrowing_types_page.dart';

class _BorrowingTypesTable extends StatelessWidget {
  const _BorrowingTypesTable({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final local = AppLocalizations.of(context)!;

    List<String> titles = [
      local.nameAr,
      local.nameEn,
      local.notes,
      local.actions
    ];

    List<Widget> cells({
      required BorrowingTypeEntity borrowingType,
    }) {
      return [
        CellWidget(borrowingType.nameAr!),
        CellWidget(borrowingType.nameEn!),
        CellWidget(borrowingType.notes!),
        ActionButtons(
            onEdit: () => NV.nextScreen(
                context, AddBorrowingTypePage(borrowingType: borrowingType)),
            onDelete: () => showPlatformDialog(
                  context,
                  isDelete: true,
                  title: local.deleteBorrowingType,
                  content: local.deleteBorrowingTypeMessage,
                  action: () => context
                      .read<BorrowingTypesCubit>()
                      .deleteBorrowingType(local, id: borrowingType.id!),
                )),
      ];
    }

    return _Table(
      titles: titles,
      cells: cells,
    );
  }
}

class _Table extends HookWidget {
  final List<String> titles;
  final List<Widget> Function({required BorrowingTypeEntity borrowingType})
      cells;

  const _Table({Key? key, required this.titles, required this.cells})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BorrowingTypesCubit, BorrowingTypesState>(
        builder: (context, borrowingTypesState) {
      if (borrowingTypesState is ErrorBorrowingTypesState) {
        return Center(
          child: Text(borrowingTypesState.message.translateErrorMessage),
        );
      }

      final showFooter = borrowingTypesState is LoadedBorrowingTypesState &&
          borrowingTypesState.borrowingTypes.isNotEmpty;

      //! Titles Row
      final columnWidget = titles
          .map(
            (e) => CellWidget(e, isTitle: true),
          )
          .toList(growable: false);

      //! Data Rows
      List<DataRow> rowWidget() {
        if (borrowingTypesState is LoadingBorrowingTypesState ||
            borrowingTypesState is LoadingAddUpdateDeleteBorrowingTypeState) {
          return LoadingWidget.loadingTable(titles.length);
        } else if (borrowingTypesState is LoadedBorrowingTypesState) {
          return borrowingTypesState.borrowingTypes
              .map((e) => DataRow(
                    cells: cells(borrowingType: e)
                        .map((e) => DataCell(e))
                        .toList(),
                  ))
              .toList(growable: false);
        }
        return [];
      }

      //! Table Body
      Widget tableBody() {
        void onPerPageChanged(value) {
          BorrowingTypesCubit.get(context).selectedPerPageEntries.value = value;
          BorrowingTypesCubit.get(context).getAllBorrowingTypes();
        }

        return CustomTable(
          selectedPerPageEntriesValue:
              BorrowingTypesCubit.get(context).selectedPerPageEntries,
          onChangePerPage: onPerPageChanged,
          showFooter: showFooter,
          columns: columnWidget,
          rows: [
            //? Search Fields Row
            DataRow(
              selected: true,
              cells: titles
                  .map((e) => DataCell(
                        _SearchRow(title: e),
                      ))
                  .toList(growable: false),
            ),

            //? Data Rows
            ...rowWidget(),
          ],
        );
      }

      return Stack(
        children: [
          tableBody(),
          if (borrowingTypesState is LoadedBorrowingTypesState &&
              borrowingTypesState.borrowingTypes.isEmpty)
            const Center(
              child: EmptyLottieIcon(),
            )
        ],
      );
    });
  }
}

class _SearchRow extends HookWidget {
  final String title;

  const _SearchRow({Key? key, required this.title}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final local = AppLocalizations.of(context)!;

    if (title != local.notes && title != local.actions) {
      return BaseTableTextField(
          hintText: title,
          onChanged: (value) => BorrowingTypesCubit.get(context)
              .onSearchChanged(value, filterKeyParams: (title, local)));
    }

    return const SizedBox();
  }
}
