import 'package:dartz/dartz.dart';
import 'package:optimum_app/src/core/consts/error/failures.dart';

import '../entities/custodies_entity.dart';
import '../repositories/custodies_the_media.dart';

class AddCustodiesUseCase {
  final CustodiesRepository repository;

  AddCustodiesUseCase(this.repository);

  Future<Either<Failure, Unit>> call(CustodiesEntity custodies) async {
    return await repository.addCustodies(custodies);
  }
}
