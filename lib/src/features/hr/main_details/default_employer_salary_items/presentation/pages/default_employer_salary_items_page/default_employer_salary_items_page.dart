import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:optimum_app/src/core/adaptive_widgets/adaptive_top_bar.dart';
import 'package:optimum_app/src/core/consts/spaces/spaces.dart';
import 'package:optimum_app/src/core/consts/strings/consts.dart';
import 'package:optimum_app/src/core/extensions/extensions.dart';
import 'package:optimum_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:optimum_app/src/core/utils/logger.dart';
import 'package:optimum_app/src/features/home/<USER>/drawer/app_drawer.dart';
import 'package:optimum_app/src/features/hr/main_details/default_employer_salary_items/presentation/cubit/default_employer_salary_items_cubit.dart';
import 'package:optimum_app/src/features/hr/main_details/salary_items/domain/entities/salary_items_entity.dart';
import 'package:optimum_app/src/features/hr/main_details/salary_items/presentation/cubit/cubit.dart';

import '../../../domain/entities/default_employer_salary_items_entity.dart';

part 'widgets/default_employer_salary_items_fields.dart';
part 'widgets/default_employer_salary_items_save_button.dart';

class DefaultEmployerSalaryItemsPage extends HookWidget {
  static const routeName = '/hr/defaultEmployerSalaryItems_and_sections';

  const DefaultEmployerSalaryItemsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final local = context.local;

    final valueNotifiers = {
      Consts.absenceWithPermission: useState<SalaryItemEntity?>(null),
      Consts.absenceWithoutPermission: useState<SalaryItemEntity?>(null),
      Consts.latencyWithPermission: useState<SalaryItemEntity?>(null),
      Consts.latencyWithoutPermission: useState<SalaryItemEntity?>(null),
      Consts.earlyLeaveWithPermission: useState<SalaryItemEntity?>(null),
      Consts.earlyLeaveWithoutPermission: useState<SalaryItemEntity?>(null),
      Consts.laborRegulationViolations: useState<SalaryItemEntity?>(null),
      Consts.workerConductViolations: useState<SalaryItemEntity?>(null),
      Consts.middleShiftLeaveWithPermission: useState<SalaryItemEntity?>(null),
      Consts.middleShiftLeaveWithoutPermission:
          useState<SalaryItemEntity?>(null),
      Consts.employerInsurance: useState<SalaryItemEntity?>(null),
      Consts.companyInsurance: useState<SalaryItemEntity?>(null),
      Consts.basicSalary: useState<SalaryItemEntity?>(null),
      Consts.housingAllowance: useState<SalaryItemEntity?>(null),
      Consts.employerTaxes: useState<SalaryItemEntity?>(null),
      Consts.companyTaxes: useState<SalaryItemEntity?>(null),
      Consts.forgetSignature: useState<SalaryItemEntity?>(null),
      Consts.addition: useState<SalaryItemEntity?>(null),
      Consts.workLeave: useState<SalaryItemEntity?>(null),
      Consts.vacationsCollect: useState<SalaryItemEntity?>(null),
      Consts.ticketsCollect: useState<SalaryItemEntity?>(null),
    };

    return MultiBlocProvider(
      providers: [
        BlocProvider(
            create: (context) => DefaultEmployerSalaryItemsCubit
                .getDefaultEmployerSalaryItemsCubitSl()
              ..getAllDefaultEmployerSalaryItems()),
        BlocProvider(
          create: (context) =>
              SalaryItemsCubit.getSalaryItemsCubitSl()..getAllSalaryItems(),
        ),
      ],
      child: GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),
        child: BlocConsumer<DefaultEmployerSalaryItemsCubit,
            DefaultEmployerSalaryItemsState>(listener: (context, state) {
          Log.i('state: ${state.runtimeType}');
          if (state is ErrorDefaultEmployerSalaryItemsState) {
            context.showBar(state.message.translateErrorMessage, isError: true);
          } else if (state is SuccessDefaultEmployerSalaryItemsState) {
            context.showBar(state.message);

            DefaultEmployerSalaryItemsCubit.get(context)
                .getAllDefaultEmployerSalaryItems();
          }
        }, builder: (_, state) {
          if (state is LoadingDefaultEmployerSalaryItemsState) {
            return const LoadingWidget(
              fieldLoading: true,
            );
          } else if (state is LoadedDefaultEmployerSalaryItemsState) {
            return Scaffold(
                drawer: const AppDrawer(),
                bottomNavigationBar: _DefaultEmployerSalaryItemSaveButton(
                  valueNotifiers: valueNotifiers,
                  defaultEmployerSalaryItem: state.defaultEmployerSalaryItem,
                ),
                appBar: AdaptiveTopBar(
                  title: local.defaultEmployerSalaryItems,
                ),
                body: GestureDetector(
                  onTap: () => FocusScope.of(context).unfocus(),
                  child: BlocConsumer<DefaultEmployerSalaryItemsCubit,
                          DefaultEmployerSalaryItemsState>(
                      listener: (context, state) {
                    Log.i('state: ${state.runtimeType}');
                    if (state is ErrorDefaultEmployerSalaryItemsState) {
                      context.showBar(state.message.translateErrorMessage,
                          isError: true);
                    } else if (state
                        is SuccessDefaultEmployerSalaryItemsState) {
                      context.showBar(state.message);

                      DefaultEmployerSalaryItemsCubit.get(context)
                          .getAllDefaultEmployerSalaryItems();
                    }
                  }, builder: (_, state) {
                    if (state is LoadingDefaultEmployerSalaryItemsState) {
                      return const LoadingWidget(
                        fieldLoading: true,
                      );
                    } else if (state is LoadedDefaultEmployerSalaryItemsState) {
                      return _DefaultEmployerSalaryItemsFields(
                          defaultEmployerSalaryItem:
                              state.defaultEmployerSalaryItem,
                          valueNotifiers: valueNotifiers);
                    }

                    return const SizedBox.shrink();
                  }),
                ));
          }

          return const SizedBox.shrink();
        }),
      ),
    );
  }
}
