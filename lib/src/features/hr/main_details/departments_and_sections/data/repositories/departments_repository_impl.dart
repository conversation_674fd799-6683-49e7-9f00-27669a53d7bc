import 'dart:io';

import 'package:dartz/dartz.dart';
import 'package:optimum_app/src/core/consts/error/exceptions.dart';
import 'package:optimum_app/src/core/consts/error/failures.dart';

import '../../domain/entities/department_entity.dart';
import '../../domain/repositories/departments_repository.dart';
import '../data_sources/departments_data_source.dart';

class DepartmentsRepositoryImpl implements DepartmentsRepository {
  final DepartmentsRemoteDataSource remoteDataSource;

  DepartmentsRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, DepartmentEntity>> getDepartments({
    String? columnFilters,
    int? perPage,
  }) async {
    try {
      final remoteDepartments = await remoteDataSource.getAllDepartments(
        columnFilters: columnFilters,
        perPage: perPage,
      );
      return Right(remoteDepartments);
    } on ServerException {
      return Left(ServerFailure());
    } on SocketException {
      return Left(OfflineFailure());
    }
  }

  @override
  Future<Either<Failure, List<DepartmentEntity>>>
      getDepartmentsAndSections() async {
    try {
      final remoteDepartments =
          await remoteDataSource.getDepartmentsAndSections();
      return Right(remoteDepartments);
    } on ServerException {
      return Left(ServerFailure());
    } on SocketException {
      return Left(OfflineFailure());
    }
  }

  @override
  Future<Either<Failure, Unit>> addDepartment(
      DepartmentEntity department) async {
    try {
      await remoteDataSource
          .addDepartment(DepartmentEntity.toModel(department));
      return const Right(unit);
    } on ServerException {
      return Left(ServerFailure());
    } on SocketException {
      return Left(OfflineFailure());
    }
  }

  @override
  Future<Either<Failure, Unit>> updateDepartment(
      DepartmentEntity department) async {
    try {
      await remoteDataSource
          .updateDepartment(DepartmentEntity.toModel(department));
      return const Right(unit);
    } on ServerException {
      return Left(ServerFailure());
    } on SocketException {
      return Left(OfflineFailure());
    }
  }

  @override
  Future<Either<Failure, Unit>> deleteDepartment(int id) async {
    try {
      await remoteDataSource.deleteDepartment(id);
      return const Right(unit);
    } on ServerException {
      return Left(ServerFailure());
    } on SocketException {
      return Left(OfflineFailure());
    }
  }
}
