import 'package:optimum_app/src/core/data/remote/response/api_strings.dart';
import 'package:optimum_app/src/features/settings/general_settings/domain/entities/general_settings_entity.dart';

import '../../domain/entities/discount_types_entity.dart';

List<DiscountTypeModel> responseToDiscountTypes(response) {
  final discountTypes = List<DiscountTypeModel>.from(
    response[ApiStrings.data][ApiStrings.data].map(
      (x) => DiscountTypeModel.fromJson(x),
    ),
  );

  return discountTypes;
}

class DiscountTypeModel extends DiscountTypeEntity {
  const DiscountTypeModel({
    super.id,
    super.name,
    super.nameAr,
    super.nameEn,
    super.notes,
    super.createdAt,
    super.isDays,
    super.noDays,
    super.value,
    super.salaryItemId,
  });

  factory DiscountTypeModel.fromJson(Map<String, dynamic> json) {
    return DiscountTypeModel(
      id: json['id'],
      name: json['name'] ?? '',
      nameAr: json['name_ar'] ?? '',
      nameEn: json['name_en'] ?? '',
      notes: json['notes'] ?? '',
      createdAt: json['created_at'],
      isDays: json['is_days']?.toString() ?? '',
      noDays: json['days']?.toString() ?? '',
      value: json['value']?.toString() ?? '',
      salaryItemId: json['salary_item_id'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (id != null) {
      data['id'] = id;
    }

    data['branch_id'] = MainGeneralSettingsEntity.branchId;
    data['name_ar'] = nameAr;
    data['name_en'] = nameEn;
    data['is_days'] = isDays;
    if (isDays == '1') {
      data['days'] = noDays;
      data['value'] = null;
    } else {
      data['value'] = value;
      data['days'] = null;
    }

    data['salary_item_id'] = salaryItemId;

    data['serial'] = '';

    data['notes'] = notes;

    return data;
  }
}
