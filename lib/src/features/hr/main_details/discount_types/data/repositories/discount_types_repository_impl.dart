import 'dart:io';

import 'package:dartz/dartz.dart';
import 'package:optimum_app/src/core/consts/error/exceptions.dart';
import 'package:optimum_app/src/core/consts/error/failures.dart';

import '../../domain/entities/discount_types_entity.dart';
import '../../domain/repositories/discount_types_repository.dart';
import '../data_sources/discount_types_data_source.dart';

class DiscountTypesRepositoryImpl implements DiscountTypesRepository {
  final DiscountTypesRemoteDataSource remoteDataSource;

  DiscountTypesRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, List<DiscountTypeEntity>>> getDiscountTypes({
    String? columnFilters,
    int? perPage,
  }) async {
    try {
      final remoteDiscountTypes = await remoteDataSource.getAllDiscountTypes(
        columnFilters: columnFilters,
        perPage: perPage,
      );
      return Right(remoteDiscountTypes);
    } on ServerException {
      return Left(ServerFailure());
    } on SocketException {
      return Left(OfflineFailure());
    }
  }

  @override
  Future<Either<Failure, Unit>> addDiscountType(
      DiscountTypeEntity discountType) async {
    try {
      await remoteDataSource
          .addDiscountType(DiscountTypeEntity.toModel(discountType));
      return const Right(unit);
    } on ServerException {
      return Left(ServerFailure());
    } on SocketException {
      return Left(OfflineFailure());
    }
  }

  @override
  Future<Either<Failure, Unit>> updateDiscountType(
      DiscountTypeEntity discountType) async {
    try {
      await remoteDataSource
          .updateDiscountType(DiscountTypeEntity.toModel(discountType));
      return const Right(unit);
    } on ServerException {
      return Left(ServerFailure());
    } on SocketException {
      return Left(OfflineFailure());
    }
  }

  @override
  Future<Either<Failure, Unit>> deleteDiscountType(int id) async {
    try {
      await remoteDataSource.deleteDiscountType(id);
      return const Right(unit);
    } on ServerException {
      return Left(ServerFailure());
    } on SocketException {
      return Left(OfflineFailure());
    }
  }
}
