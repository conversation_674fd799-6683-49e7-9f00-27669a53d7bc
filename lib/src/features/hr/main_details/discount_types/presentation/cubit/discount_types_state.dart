part of 'discount_types_cubit.dart';

abstract class DiscountTypesState extends Equatable {
  const DiscountTypesState();

  @override
  List<Object> get props => [];
}

class DiscountTypesInitialState extends DiscountTypesState {}

//? Get DiscountType States
class LoadingDiscountTypesState extends DiscountTypesState {}

class LoadedDiscountTypesState extends DiscountTypesState {
  final List<DiscountTypeEntity> discountTypes;

  const LoadedDiscountTypesState({required this.discountTypes});

  @override
  List<Object> get props => [discountTypes];
}

//? Add & Update & Delete DiscountType States
class LoadingAddUpdateDeleteDiscountTypeState extends DiscountTypesState {}

class SuccessDiscountTypesState extends DiscountTypesState {
  final String message;

  const SuccessDiscountTypesState({required this.message});

  @override
  List<Object> get props => [message];
}

//? Error DiscountType State
class ErrorDiscountTypesState extends DiscountTypesState {
  final String message;

  const ErrorDiscountTypesState({required this.message});

  @override
  List<Object> get props => [message];
}
