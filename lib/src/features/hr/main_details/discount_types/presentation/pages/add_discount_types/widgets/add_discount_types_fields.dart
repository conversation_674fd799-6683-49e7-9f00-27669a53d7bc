part of '../add_discount_types_page.dart';

class _AddDiscountTypeFields extends HookWidget {
  final Map<String, TextEditingController> controllers;
  final Map<String, ValueNotifier> valueNotifiers;
  final DiscountTypeEntity? discountType;

  const _AddDiscountTypeFields(
      {Key? key,
      required this.controllers,
      required this.valueNotifiers,
      this.discountType})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final local = AppLocalizations.of(context)!;
    Widget fields() => Column(
          children: [
            //! Salary Item
            SalaryItemsDropDown(
              selectedSalaryItem: valueNotifiers[Consts.selectedSalaryItem]
                  as ValueNotifier<SalaryItemEntity?>,
              salaryItemId: discountType?.salaryItemId!,
            ),

            context.fieldSpace,

            //! Name Ar
            BaseTextField(
                title: local.nameAr,
                controller: controllers[Consts.nameAr],
                hintText: local.nameAr,
                icon: const BaseLottieFieldIcon(
                  AnimatedAssets.userField,
                )),

            context.fieldSpace,

            //! Name En
            BaseTextField(
                title: local.nameEn,
                controller: controllers[Consts.nameEn],
                hintText: local.nameEn,
                icon: const BaseLottieFieldIcon(
                  AnimatedAssets.userField,
                )),

            context.fieldSpace,

            //! In Days
            BaseSingleCheckTile(
                onChanged: (value) {
                  valueNotifiers[Consts.inDays]!.value = value ? '1' : '0';
                },
                title: local.inDays,
                value: valueNotifiers[Consts.inDays]!.value == '1'),

            context.fieldSpace,

            //! No Days
            BaseTextField(
                enabled: valueNotifiers[Consts.inDays]!.value == '1',
                title: local.noDays,
                controller: controllers[Consts.noDays],
                hintText: local.noDays,
                isRequired: valueNotifiers[Consts.inDays]!.value == '1',
                icon: const BaseLottieFieldIcon(AnimatedAssets.priceField,
                    height: 45)),

            context.fieldSpace,

            //! Value
            BaseTextField(
                enabled: valueNotifiers[Consts.inDays]!.value == '0',
                title: local.value,
                controller: controllers[Consts.value],
                hintText: local.value,
                isRequired: valueNotifiers[Consts.inDays]!.value == '0',
                icon: const BaseLottieFieldIcon(AnimatedAssets.priceField,
                    height: 45)),

            context.fieldSpace,

            //! Notes
            BaseTextField(
                title: local.notes,
                controller: controllers[Consts.notes],
                hintText: local.notes,
                isRequired: false,
                icon: const BaseLottieFieldIcon(AnimatedAssets.notesField,
                    height: 45)),
          ],
        );

    return ListView(
      padding: const EdgeInsets.all(AppSpaces.large),
      children: [
        BlocBuilder<DiscountTypesCubit, DiscountTypesState>(
          builder: (context, discountTypesState) {
            if (discountTypesState is LoadingAddUpdateDeleteDiscountTypeState) {
              return const LoadingWidget(fieldLoading: true);
            } else {
              return fields();
            }
          },
        ),
      ],
    );
  }
}
