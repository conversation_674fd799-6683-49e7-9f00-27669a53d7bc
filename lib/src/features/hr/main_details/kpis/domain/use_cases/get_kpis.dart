import 'package:dartz/dartz.dart';
import 'package:optimum_app/src/core/consts/error/failures.dart';

import '../entities/kpis_entity.dart';
import '../repositories/kpis_repository.dart';

class GetKPIsUseCase {
  final KPIsRepository repository;

  GetKPIsUseCase(this.repository);

  Future<Either<Failure, List<KPIEntity>>> call({
    String? columnFilters,
    int? perPage,
  }) async {
    return await repository.getKPIs(
      columnFilters: columnFilters,
      perPage: perPage,
    );
  }
}
