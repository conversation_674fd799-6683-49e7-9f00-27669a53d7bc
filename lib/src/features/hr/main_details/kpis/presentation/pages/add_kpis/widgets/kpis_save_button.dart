part of '../add_kpis_page.dart';

class _KPISaveButton extends HookWidget {
  final Map<String, TextEditingController> controllers;
  final KPIEntity? kPI;

  const _KPISaveButton({
    Key? key,
    required this.controllers,
    this.kPI,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final local = AppLocalizations.of(context)!;

    void onSaveKPI() async {
      final formKey = KPIsCubit.formKey;
      final isEdit = kPI != null;

      if (formKey.currentState!.validate()) {
        final fields = controllers;

        if (isEdit) {
          context
              .read<KPIsCubit>()
              .updateKPI(local, id: kPI!.id!, controllers: fields);
        } else {
          context.read<KPIsCubit>().addKPI(local, controllers: fields);
        }
      }
    }

    return BlocBuilder<KPIsCubit, KPIsState>(builder: (context, state) {
      final isLoading = state is LoadingAddUpdateDeleteKPIState;
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (isLoading)
            const LoadingWidget(
              isLinear: true,
            ),
          BaseSaveButton(onPressed: isLoading ? null : () => onSaveKPI()),
        ],
      );
    });
  }
}
