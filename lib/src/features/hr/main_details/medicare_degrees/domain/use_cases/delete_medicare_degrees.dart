import 'package:dartz/dartz.dart';
import 'package:optimum_app/src/core/consts/error/failures.dart';

import '../repositories/medicare_degrees_repository.dart';

class DeleteMedicareDegreesUseCase {
  final MedicareDegreesRepository repository;

  DeleteMedicareDegreesUseCase(this.repository);

  Future<Either<Failure, Unit>> call(int id) async {
    return await repository.deleteMedicareDegrees(id);
  }
}
