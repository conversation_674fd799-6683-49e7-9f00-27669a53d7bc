import 'package:dartz/dartz.dart';
import 'package:optimum_app/src/core/consts/error/failures.dart';

import '../entities/medicare_degrees_entity.dart';
import '../repositories/medicare_degrees_repository.dart';

class UpdateMedicareDegreesUseCase {
  final MedicareDegreesRepository repository;

  UpdateMedicareDegreesUseCase(this.repository);

  Future<Either<Failure, Unit>> call(
    MedicareDegreesEntity medicareDegrees,
  ) async {
    return await repository.updateMedicareDegrees(medicareDegrees);
  }
}
