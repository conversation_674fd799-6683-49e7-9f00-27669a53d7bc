part of 'medicare_degrees_cubit.dart';

abstract class MedicareDegreesState extends Equatable {
  const MedicareDegreesState();

  @override
  List<Object> get props => [];
}

class MedicareDegreesInitialState extends MedicareDegreesState {}

//? Get medicareDegrees States
class LoadingMedicareDegreesState extends MedicareDegreesState {}

class LoadedMedicareDegreesState extends MedicareDegreesState {
  final List<MedicareDegreesEntity> medicareDegrees;

  const LoadedMedicareDegreesState({required this.medicareDegrees});

  @override
  List<Object> get props => [medicareDegrees];
}

//? Add & Update & Delete medicareDegrees States
class LoadingAddUpdateDeleteMedicareDegreestate extends MedicareDegreesState {}

class SuccessMedicareDegreesState extends MedicareDegreesState {
  final String message;

  const SuccessMedicareDegreesState({required this.message});

  @override
  List<Object> get props => [message];
}

//? Error medicareDegrees State
class ErrorMedicareDegreesState extends MedicareDegreesState {
  final String message;

  const ErrorMedicareDegreesState({required this.message});

  @override
  List<Object> get props => [message];
}
