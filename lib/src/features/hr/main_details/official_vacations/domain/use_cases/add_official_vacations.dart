import 'package:dartz/dartz.dart';
import 'package:optimum_app/src/core/consts/error/failures.dart';

import '../entities/official_vacations_entity.dart';
import '../repositories/official_vacations_repository.dart';

class AddOfficialVacationUseCase {
  final OfficialVacationsRepository repository;

  AddOfficialVacationUseCase(this.repository);

  Future<Either<Failure, Unit>> call(
      OfficialVacationEntity officialVacation) async {
    return await repository.addOfficialVacation(officialVacation);
  }
}
