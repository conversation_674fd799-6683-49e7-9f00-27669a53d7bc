import 'dart:core';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:optimum_app/src/core/adaptive_widgets/adaptive_top_bar.dart';
import 'package:optimum_app/src/core/consts/assets/assets.dart';
import 'package:optimum_app/src/core/consts/spaces/spaces.dart';
import 'package:optimum_app/src/core/consts/strings/consts.dart';
import 'package:optimum_app/src/core/extensions/extensions.dart';
import 'package:optimum_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:optimum_app/src/core/utils/nav.dart';

import '../../../domain/entities/official_vacations_entity.dart';
import '../../cubit/cubit.dart';
import '../../cubit/official_vacations_cubit.dart';
import '../official_vacations/official_vacations_page.dart';

part 'widgets/add_official_vacations_fields.dart';
part 'widgets/official_vacations_save_button.dart';

class AddOfficialVacationPage extends HookWidget {
  final OfficialVacationEntity? officialVacation;

  static const routeName = '/hr/official_vacations/add';

  const AddOfficialVacationPage({super.key, this.officialVacation});

  @override
  Widget build(BuildContext context) {
    final local = AppLocalizations.of(context)!;

    //? Add Controllers to Map to reduce parameters passing
    final Map<String, TextEditingController> controllers = {
      Consts.nameAr: useTextEditingController(text: officialVacation?.nameAr),
      Consts.nameEn: useTextEditingController(text: officialVacation?.nameEn),
      Consts.notes: useTextEditingController(text: officialVacation?.notes),
    };

    final isEdit = officialVacation != null;

    return WillPopScope(
      onWillPop: () async {
        NV.nextScreenCloseOthersNamed(context, OfficialVacationsPage.routeName);
        return false;
      },
      child: BlocProvider(
        create: (context) =>
            OfficialVacationsCubit.getOfficialVacationsCubitSl(),
        child: BlocListener<OfficialVacationsCubit, OfficialVacationsState>(
            listener: (context, state) {
              log('officialVacationState => ${state.runtimeType}');
              if (state is ErrorOfficialVacationsState) {
                Navigator.pop(context);
                NV.nextScreenNamed(context, OfficialVacationsPage.routeName);

                context.showBar(state.message.translateErrorMessage,
                    isError: true);
              } else if (state is SuccessOfficialVacationsState) {
                Navigator.pop(context);
                NV.nextScreenCloseOthersNamed(
                    context, OfficialVacationsPage.routeName);

                context.showBar(state.message);
              }
            },
            child: Scaffold(
              appBar: AdaptiveTopBar(
                title: isEdit
                    ? local.editOfficialVacation
                    : local.addOfficialVacation,
              ),
              bottomNavigationBar: _OfficialVacationSaveButton(
                controllers: controllers,
                officialVacation: officialVacation,
              ),
              body: Form(
                key: OfficialVacationsCubit.formKey,
                child: _AddOfficialVacationFields(
                  controllers: controllers,
                  officialVacation: officialVacation,
                ),
              ),
            )),
      ),
    );
  }
}
