import 'package:dartz/dartz.dart';
import 'package:optimum_app/src/core/consts/error/failures.dart';

import '../entities/organizations_entity.dart';

abstract class OrganizationsRepository {
  Future<Either<Failure, List<OrganizationEntity>>> getOrganizations(
      {String? columnFilters, int? perPage});
  Future<Either<Failure, Unit>> addOrganization(
      OrganizationEntity organization);
  Future<Either<Failure, Unit>> updateOrganization(
      OrganizationEntity organization);
  Future<Either<Failure, Unit>> deleteOrganization(int id);
}
