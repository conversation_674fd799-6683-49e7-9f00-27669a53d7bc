part of '../add_organizations_page.dart';

class _AddOrganizationFields extends HookWidget {
  final Map<String, TextEditingController> controllers;
  final OrganizationEntity? organization;
  final ValueNotifier<String> selectedType;

  const _AddOrganizationFields({
    Key? key,
    required this.controllers,
    required this.selectedType,
    this.organization,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final local = AppLocalizations.of(context)!;

    final types = [
      "universities",
      "organizations",
    ];

    Widget fields() => Column(
          children: [
            //! Type DropDown
            BaseDropDown(
                data: types,
                onChanged: (value) {
                  selectedType.value = value;
                },
                label: local.type,
                selectedValue: selectedType.value),

            context.fieldSpace,

            //! Name Ar
            BaseTextField(
                title: local.nameAr,
                controller: controllers[Consts.nameAr],
                hintText: local.nameAr,
                icon: const BaseLottieFieldIcon(
                  AnimatedAssets.userField,
                )),

            context.fieldSpace,

            //! Name En
            BaseTextField(
                title: local.nameEn,
                controller: controllers[Consts.nameEn],
                hintText: local.nameEn,
                icon: const BaseLottieFieldIcon(
                  AnimatedAssets.userField,
                )),

            context.fieldSpace,

            //! Notes
            BaseTextField(
                title: local.notes,
                controller: controllers[Consts.notes],
                hintText: local.notes,
                isRequired: false,
                icon: const BaseLottieFieldIcon(AnimatedAssets.notesField,
                    height: 45)),
          ],
        );

    return ListView(
      padding: const EdgeInsets.all(AppSpaces.large),
      children: [
        BlocBuilder<OrganizationsCubit, OrganizationsState>(
          builder: (context, organizationsState) {
            if (organizationsState is LoadingAddUpdateDeleteOrganizationState) {
              return const LoadingWidget(fieldLoading: true);
            } else {
              return fields();
            }
          },
        ),
      ],
    );
  }
}
