part of '../add_other_costs_page.dart';

class _OtherCostSaveButton extends HookWidget {
  final Map<String, TextEditingController> controllers;
  final OtherCostEntity? otherCost;

  const _OtherCostSaveButton({
    Key? key,
    required this.controllers,
    this.otherCost,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final local = AppLocalizations.of(context)!;

    void onSaveOtherCost() async {
      final formKey = OtherCostsCubit.formKey;
      final isEdit = otherCost != null;

      if (formKey.currentState!.validate()) {
        final fields = controllers;

        if (isEdit) {
          context
              .read<OtherCostsCubit>()
              .updateOtherCost(local, id: otherCost!.id!, controllers: fields);
        } else {
          context
              .read<OtherCostsCubit>()
              .addOtherCost(local, controllers: fields);
        }
      }
    }

    return BlocBuilder<OtherCostsCubit, OtherCostsState>(
        builder: (context, state) {
      final isLoading = state is LoadingAddUpdateDeleteOtherCostState;
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (isLoading)
            const LoadingWidget(
              isLinear: true,
            ),
          BaseSaveButton(onPressed: isLoading ? null : () => onSaveOtherCost()),
        ],
      );
    });
  }
}
