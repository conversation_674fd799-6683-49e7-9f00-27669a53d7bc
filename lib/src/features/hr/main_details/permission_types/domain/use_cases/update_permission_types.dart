import 'package:dartz/dartz.dart';
import 'package:optimum_app/src/core/consts/error/failures.dart';

import '../entities/permission_types_entity.dart';
import '../repositories/permission_types_the_media.dart';

class UpdatePermissionTypesUseCase {
  final PermissionTypesRepository repository;

  UpdatePermissionTypesUseCase(this.repository);

  Future<Either<Failure, Unit>> call(
    PermissionTypesEntity permissionTypes,
  ) async {
    return await repository.updatePermissionTypes(permissionTypes);
  }
}
