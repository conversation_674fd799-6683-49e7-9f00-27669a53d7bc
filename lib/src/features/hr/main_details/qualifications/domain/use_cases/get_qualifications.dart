import 'package:dartz/dartz.dart';
import 'package:optimum_app/src/core/consts/error/failures.dart';
import 'package:optimum_app/src/features/hr/main_details/qualifications/domain/repositories/qualifications_repository.dart';

import '../entities/qualifications_entity.dart';

class GetQualificationsUseCase {
  final QualificationsRepository repository;

  GetQualificationsUseCase(this.repository);

  Future<Either<Failure, List<QualificationsEntity>>> call({
    String? columnFilters,
    int? perPage,
  }) async {
    return await repository.getQualifications(
      columnFilters: columnFilters,
      perPage: perPage,
    );
  }
}
