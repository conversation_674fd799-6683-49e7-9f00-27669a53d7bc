import 'dart:convert';

import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:optimum_app/src/core/consts/error/failures.dart';
import 'package:optimum_app/src/core/consts/strings/consts.dart';
import 'package:optimum_app/src/core/extensions/extensions.dart';
import 'package:optimum_app/src/core/utils/injector/injector.dart';
import 'package:optimum_app/src/features/hr/main_details/qualifications/domain/use_cases/add_qualification.dart';
import 'package:optimum_app/src/features/hr/main_details/qualifications/domain/use_cases/delete_qualification.dart';
import 'package:optimum_app/src/features/hr/main_details/qualifications/domain/use_cases/get_qualifications.dart';
import 'package:optimum_app/src/features/hr/main_details/qualifications/domain/use_cases/update_qualification.dart';

import '../../domain/entities/qualifications_entity.dart';

part 'qualifications_state.dart';

class QualificationsCubit extends Cubit<QualificationsState> {
  final GetQualificationsUseCase getAllQualificationsUseCase;
  final AddQualificationUseCase addQualificationsUseCase;
  final UpdateQualificationUseCase updateQualificationsUseCase;
  final DeleteQualificationUseCase deleteQualificationsUseCase;

  QualificationsCubit({
    required this.getAllQualificationsUseCase,
    required this.addQualificationsUseCase,
    required this.updateQualificationsUseCase,
    required this.deleteQualificationsUseCase,
  }) : super(QualificationsInitialState());

  //? Get qualifications Cubit
  static QualificationsCubit get(context) => BlocProvider.of(context);

  //? Get qualifications Dependency Injection Cubit
  static QualificationsCubit getQualificationsCubitSl() =>
      sl<QualificationsCubit>();

  //? Api Filters & Pagination
  final columnFilters = <String, String>{};

  final ValueNotifier<int> selectedPerPageEntries = ValueNotifier(10);

  //! Get qualifications ----------------------------

  void getAllQualifications() async {
    try {
      emit(LoadingQualificationsState());

      final result = await getAllQualificationsUseCase.call(
        columnFilters: jsonEncode(columnFilters),
        perPage: selectedPerPageEntries.value,
      );

      emit(_eitherGetQualificationOrErrorState(result));
    } catch (e) {
      emit(ErrorQualificationsState(
          message: e.toString().translateErrorMessage));
    }
  }

  QualificationsState _eitherGetQualificationOrErrorState(
      Either<Failure, List<QualificationsEntity>> result) {
    return result.fold(
        (failure) => ErrorQualificationsState(
            message: failure.toString().translateErrorMessage),
        (qualifications) =>
            LoadedQualificationsState(qualifications: qualifications));
  }

  //! Add & Update & Delete qualifications ----------------------------

  //? Form Key for Validating Add Or Update qualification
  static GlobalKey<FormState> formKey = GlobalKey<FormState>();

  Future<void> addQualification(AppLocalizations local,
      {required Map<String, TextEditingController> controllers}) async {
    try {
      emit(LoadingAddUpdateDeleteQualificationState());

      final qualificationEntity = QualificationsEntity(
        nameAr: controllers[Consts.nameAr]!.text,
        nameEn: controllers[Consts.nameEn]!.text,
        notes: controllers[Consts.notes]!.text,
      );

      final result = await addQualificationsUseCase(qualificationEntity);

      emit(_eitherDoneMessageOrErrorState(result, local.addedSuccessfully));
    } catch (e) {
      emit(ErrorQualificationsState(
          message: e.toString().translateErrorMessage));
    }
  }

  void updatequalification(AppLocalizations local,
      {required int id,
      required Map<String, TextEditingController> controllers}) async {
    try {
      emit(LoadingAddUpdateDeleteQualificationState());

      final qualificationsEntity = QualificationsEntity(
        id: id,
        nameAr: controllers[Consts.nameAr]!.text,
        nameEn: controllers[Consts.nameEn]!.text,
        notes: controllers[Consts.notes]!.text,
      );

      final result = await updateQualificationsUseCase(qualificationsEntity);

      emit(_eitherDoneMessageOrErrorState(result, local.updatedSuccessfully));
    } catch (e) {
      emit(ErrorQualificationsState(
          message: e.toString().translateErrorMessage));
    }
  }

  void deletequalification(AppLocalizations local, {required int id}) async {
    try {
      emit(LoadingAddUpdateDeleteQualificationState());

      final result = await deleteQualificationsUseCase(id);

      emit(_eitherDoneMessageOrErrorState(result, local.deletedSuccessfully));
    } catch (e) {
      emit(ErrorQualificationsState(
          message: e.toString().translateErrorMessage));
    }
  }

  _eitherDoneMessageOrErrorState(
    Either<Failure, Unit> failureOrDoneMessage,
    String message,
  ) {
    return failureOrDoneMessage.fold(
        (failure) => ErrorQualificationsState(
            message: failure.toString().translateErrorMessage),
        (unit) => SuccessQualificationsState(message: message));
  }

  //? Search qualifications ----------------------------
  void onSearchChanged(
    String value, {
    required (String title, AppLocalizations local) filterKeyParams,
  }) {
    final filterKey = this.filterKey(filterKeyParams: filterKeyParams);

    columnFilters.addAll({filterKey: value});

    getAllQualifications();
  }

  String filterKey({
    required (String title, AppLocalizations local) filterKeyParams,
  }) {
    final title = filterKeyParams.$1;
    final local = filterKeyParams.$2;

    late String filterKey = '';
    if (title == local.nameAr) filterKey = 'name_ar';
    if (title == local.nameEn) filterKey = 'name_en';
    return filterKey;
  }
}
