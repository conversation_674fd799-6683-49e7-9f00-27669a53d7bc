part of '../add_qualifications_page.dart';

class _AddQualificationsFields extends HookWidget {
  final Map<String, TextEditingController> controllers;
  final QualificationsEntity? qualification;

  const _AddQualificationsFields(
      {Key? key, required this.controllers, this.qualification})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final local = AppLocalizations.of(context)!;
    Widget fields() => Column(
          children: [
            //! Name Ar
            BaseTextField(
                title: local.nameAr,
                controller: controllers[Consts.nameAr],
                hintText: local.nameAr,
                icon: const BaseLottieFieldIcon(
                  AnimatedAssets.userField,
                )),

            context.fieldSpace,

            //! Name En
            BaseTextField(
                title: local.nameEn,
                controller: controllers[Consts.nameEn],
                hintText: local.nameEn,
                icon: const BaseLottieFieldIcon(
                  AnimatedAssets.userField,
                )),

            context.fieldSpace,

            //! Notes
            BaseTextField(
                title: local.notes,
                controller: controllers[Consts.notes],
                hintText: local.notes,
                isRequired: false,
                icon: const BaseLottieFieldIcon(AnimatedAssets.notesField,
                    height: 45)),
          ],
        );

    return ListView(
      padding: const EdgeInsets.all(AppSpaces.large),
      children: [
        BlocBuilder<QualificationsCubit, QualificationsState>(
          builder: (context, QualificationsState) {
            if (QualificationsState
                is LoadingAddUpdateDeleteQualificationState) {
              return const LoadingWidget(fieldLoading: true);
            } else {
              return fields();
            }
          },
        ),
      ],
    );
  }
}
