import 'dart:developer';

import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';
import 'package:optimum_app/src/core/data/remote/network/api_end_points.dart';
import 'package:optimum_app/src/core/data/remote/network/base_api_service.dart';

import '../models/reward_types_model.dart';

abstract class RewardTypesRemoteDataSource {
  Future<List<RewardTypeModel>> getAllRewardTypes(
      {String? columnFilters, int? perPage});

  Future<Unit> addRewardType(RewardTypeModel rewardType);

  Future<Unit> updateRewardType(RewardTypeModel rewardType);

  Future<Unit> deleteRewardType(int id);
}

class RewardTypesRemoteDataSourceImpl extends RewardTypesRemoteDataSource {
  final BaseApiService _apiService;

  RewardTypesRemoteDataSourceImpl(this._apiService);

  @override
  Future<List<RewardTypeModel>> getAllRewardTypes(
      {String? columnFilters, int? perPage}) async {
    final endPoint =
        '${HRModuleURLs.rewardTypes}?columnFilters=$columnFilters&sortField=id&sortType=Desc&perPage=$perPage&page=1';
    try {
      final response = await _apiService.getResponse(
        endPoint,
      );

      final rewardTypes = compute(
        responseToRewardTypes,
        response,
      );

      return rewardTypes;
    } catch (error) {
      log('RewardTypeRemoteDataSourceImplError: $error');
      rethrow;
    }
  }

  @override
  Future<Unit> addRewardType(RewardTypeModel rewardType) async {
    const endPoint = HRModuleURLs.rewardTypes;
    try {
      log('RewardTypeRemoteDataSourceImpl: ${rewardType.toJson()}');
      await _apiService.postResponse(
        endPoint,
        data: rewardType.toJson(),
      );

      return unit;
    } catch (error) {
      log('RewardTypeRemoteDataSourceImplError: $error');
      rethrow;
    }
  }

  @override
  Future<Unit> updateRewardType(RewardTypeModel rewardType) async {
    final endPoint = '${HRModuleURLs.rewardTypes}/${rewardType.id}';

    try {
      await _apiService.putResponse(
        endPoint,
        data: rewardType.toJson(),
      );

      return unit;
    } catch (error) {
      log('RewardTypeRemoteDataSourceImplError: $error');
      rethrow;
    }
  }

  @override
  Future<Unit> deleteRewardType(int id) async {
    final endPoint = '${HRModuleURLs.rewardTypes}/$id';

    try {
      await _apiService.deleteResponse(
        endPoint,
      );

      return unit;
    } catch (error) {
      log('RewardTypeRemoteDataSourceImplError: $error');
      rethrow;
    }
  }
}
