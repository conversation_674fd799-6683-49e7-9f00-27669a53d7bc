import 'package:dartz/dartz.dart';
import 'package:optimum_app/src/core/consts/error/failures.dart';

import '../entities/reward_types_entity.dart';
import '../repositories/reward_types_repository.dart';

class UpdateRewardTypeUseCase {
  final RewardTypesRepository repository;

  UpdateRewardTypeUseCase(this.repository);

  Future<Either<Failure, Unit>> call(
    RewardTypeEntity rewardType,
  ) async {
    return await repository.updateRewardType(rewardType);
  }
}
