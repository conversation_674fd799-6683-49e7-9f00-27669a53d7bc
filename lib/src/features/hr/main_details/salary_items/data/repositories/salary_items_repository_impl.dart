import 'dart:io';

import 'package:dartz/dartz.dart';
import 'package:optimum_app/src/core/consts/error/exceptions.dart';
import 'package:optimum_app/src/core/consts/error/failures.dart';
import 'package:optimum_app/src/features/hr/main_details/salary_items/domain/repositories/salary_items_repository.dart';

import '../../domain/entities/salary_items_entity.dart';
import '../data_sources/salary_items_data_source.dart';

class SalaryItemsRepositoryImpl implements SalaryItemsRepository {
  final SalaryItemsRemoteDataSource remoteDataSource;

  SalaryItemsRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, List<SalaryItemEntity>>> getSalaryItems({
    String? columnFilters,
    int? perPage,
  }) async {
    try {
      final remoteSalaryItems = await remoteDataSource.getAllSalaryItems(
        columnFilters: columnFilters,
        perPage: perPage,
      );
      return Right(remoteSalaryItems);
    } on ServerException {
      return Left(ServerFailure());
    } on SocketException {
      return Left(OfflineFailure());
    }
  }

  @override
  Future<Either<Failure, Unit>> addSalaryItem(
      SalaryItemEntity salaryItem) async {
    try {
      await remoteDataSource
          .addSalaryItem(SalaryItemEntity.toModel(salaryItem));
      return const Right(unit);
    } on ServerException {
      return Left(ServerFailure());
    } on SocketException {
      return Left(OfflineFailure());
    }
  }

  @override
  Future<Either<Failure, Unit>> updateSalaryItem(
      SalaryItemEntity salaryItem) async {
    try {
      await remoteDataSource
          .updateSalaryItem(SalaryItemEntity.toModel(salaryItem));
      return const Right(unit);
    } on ServerException {
      return Left(ServerFailure());
    } on SocketException {
      return Left(OfflineFailure());
    }
  }

  @override
  Future<Either<Failure, Unit>> deleteSalaryItem(int id) async {
    try {
      await remoteDataSource.deleteSalaryItem(id);
      return const Right(unit);
    } on ServerException {
      return Left(ServerFailure());
    } on SocketException {
      return Left(OfflineFailure());
    }
  }
}
