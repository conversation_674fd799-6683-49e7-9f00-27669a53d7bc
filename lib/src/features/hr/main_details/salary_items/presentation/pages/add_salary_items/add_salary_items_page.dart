import 'dart:core';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:optimum_app/src/core/adaptive_widgets/adaptive_top_bar.dart';
import 'package:optimum_app/src/core/consts/assets/assets.dart';
import 'package:optimum_app/src/core/consts/spaces/spaces.dart';
import 'package:optimum_app/src/core/consts/strings/consts.dart';
import 'package:optimum_app/src/core/extensions/extensions.dart';
import 'package:optimum_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:optimum_app/src/core/utils/nav.dart';
import 'package:optimum_app/src/features/accounting/accounts_tree/domain/entities/accounts_trees_entity.dart';
import 'package:optimum_app/src/features/accounting/accounts_tree/presentation/cubit/account_trees_cubit.dart';

import '../../../domain/entities/salary_items_entity.dart';
import '../../cubit/cubit.dart';
import '../salary_items/salary_items_page.dart';

part 'widgets/add_salary_items_fields.dart';
part 'widgets/salary_items_save_button.dart';

class AddSalaryItemPage extends HookWidget {
  final SalaryItemEntity? salaryItem;

  static const routeName = '/hr/salaryItems/add';

  const AddSalaryItemPage({super.key, this.salaryItem});

  @override
  Widget build(BuildContext context) {
    final local = AppLocalizations.of(context)!;

    //? Add Controllers to Map to reduce parameters passing
    final Map<String, TextEditingController> controllers = {
      Consts.nameAr: useTextEditingController(text: salaryItem?.nameAr),
      Consts.nameEn: useTextEditingController(text: salaryItem?.nameEn),
      Consts.shortNameAr:
          useTextEditingController(text: salaryItem?.shortNameAr),
      Consts.shortNameEn:
          useTextEditingController(text: salaryItem?.shortNameEn),
      Consts.notes: useTextEditingController(text: salaryItem?.notes),
    };

    log('asdsad ${salaryItem?.isDisabled}');

    final Map<String, ValueNotifier> valueNotifiers = {
      Consts.type: useState<String>(salaryItem?.type ?? 'addition'),
      Consts.customType: useState<String>(salaryItem?.customType ?? 'other'),
      Consts.isAffectAccounts: useState<int?>(salaryItem?.isAffectAccounts),
      Consts.isForceTotallyCalculated:
          useState<int?>(salaryItem?.isForceCalculated),
      Consts.isDisabled: useState<int?>(salaryItem?.isDisabled),
      Consts.debitAccount:
          useState<AccountTreeEntity?>(salaryItem?.debitAccount),
      Consts.creditAccount:
          useState<AccountTreeEntity?>(salaryItem?.creditAccount),
      Consts.calculatedWay: useState<String?>(salaryItem?.calculatedWay),
    };

    final isEdit = salaryItem != null;

    return WillPopScope(
      onWillPop: () async {
        NV.nextScreenCloseOthersNamed(context, SalaryItemsPage.routeName);
        return false;
      },
      child: MultiBlocProvider(
        providers: [
          BlocProvider(
            create: (context) => SalaryItemsCubit.getSalaryItemsCubitSl(),
          ),
          BlocProvider(
            create: (context) => AccountTreesCubit.getAccountsTreesCubitSl()
              ..getSearchedAccountsTrees(),
          ),
        ],
        child: BlocListener<SalaryItemsCubit, SalaryItemsState>(
            listener: (context, state) {
              log('salaryItemState => ${state.runtimeType}');
              if (state is ErrorSalaryItemsState) {
                Navigator.pop(context);
                NV.nextScreenNamed(context, SalaryItemsPage.routeName);

                context.showBar(state.message.translateErrorMessage,
                    isError: true);
              } else if (state is SuccessSalaryItemsState) {
                Navigator.pop(context);
                NV.nextScreenCloseOthersNamed(
                    context, SalaryItemsPage.routeName);

                context.showBar(state.message);
              }
            },
            child: Scaffold(
              appBar: AdaptiveTopBar(
                title: isEdit ? local.editSalaryItem : local.addSalaryItem,
              ),
              bottomNavigationBar: _SalaryItemSaveButton(
                controllers: controllers,
                valueNotifiers: valueNotifiers,
                salaryItem: salaryItem,
              ),
              body: Form(
                key: SalaryItemsCubit.formKey,
                child: _AddSalaryItemFields(
                  controllers: controllers,
                  valueNotifiers: valueNotifiers,
                  salaryItem: salaryItem,
                ),
              ),
            )),
      ),
    );
  }
}
