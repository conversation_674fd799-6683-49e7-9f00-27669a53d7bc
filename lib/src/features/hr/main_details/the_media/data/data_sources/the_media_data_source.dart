import 'dart:developer';

import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';
import 'package:optimum_app/src/core/data/remote/network/api_end_points.dart';
import 'package:optimum_app/src/core/data/remote/network/base_api_service.dart';

import '../models/the_media_model.dart';

abstract class TheMediaRemoteDataSource {
  Future<List<TheMediaModel>> getAllTheMedia(
      {String? columnFilters, int? perPage});

  Future<Unit> addTheMedia(TheMediaModel theMedia);

  Future<Unit> updateTheMedia(TheMediaModel theMedia);

  Future<Unit> deleteTheMedia(int id);
}

class TheMediaRemoteDataSourceImpl extends TheMediaRemoteDataSource {
  final BaseApiService _apiService;

  TheMediaRemoteDataSourceImpl(this._apiService);

  @override
  Future<List<TheMediaModel>> getAllTheMedia(
      {String? columnFilters, int? perPage}) async {
    final endPoint =
        '${HRModuleURLs.theMedia}?columnFilters=$columnFilters&sortField=id&sortType=Desc&perPage=$perPage&page=1';
    try {
      final response = await _apiService.getResponse(
        endPoint,
      );

      final theMedia = compute(
        responseToTheMedia,
        response,
      );

      return theMedia;
    } catch (error) {
      log('TheMediaRemoteDataSourceImplError: $error');
      rethrow;
    }
  }

  @override
  Future<Unit> addTheMedia(TheMediaModel theMedia) async {
    const endPoint = HRModuleURLs.theMedia;
    try {
      log('TheMediaRemoteDataSourceImpl: ${theMedia.toJson()}');
      await _apiService.postResponse(
        endPoint,
        data: theMedia.toJson(),
      );

      return unit;
    } catch (error) {
      log('TheMediaRemoteDataSourceImplError: $error');
      rethrow;
    }
  }

  @override
  Future<Unit> updateTheMedia(TheMediaModel theMedia) async {
    final endPoint = '${HRModuleURLs.theMedia}/${theMedia.id}';

    try {
      await _apiService.putResponse(
        endPoint,
        data: theMedia.toJson(),
      );

      return unit;
    } catch (error) {
      log('TheMediaRemoteDataSourceImplError: $error');
      rethrow;
    }
  }

  @override
  Future<Unit> deleteTheMedia(int id) async {
    final endPoint = '${HRModuleURLs.theMedia}/$id';

    try {
      await _apiService.deleteResponse(
        endPoint,
      );

      return unit;
    } catch (error) {
      log('TheMediaRemoteDataSourceImplError: $error');
      rethrow;
    }
  }
}
