import 'package:dartz/dartz.dart';
import 'package:optimum_app/src/core/consts/error/failures.dart';

import '../entities/the_media_entity.dart';
import '../repositories/the_media_the_media.dart';

class AddTheMediaUseCase {
  final TheMediaRepository repository;

  AddTheMediaUseCase(this.repository);

  Future<Either<Failure, Unit>> call(TheMediaEntity theMedia) async {
    return await repository.addTheMedia(theMedia);
  }
}
