import 'dart:core';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:optimum_app/src/core/adaptive_widgets/adaptive_top_bar.dart';
import 'package:optimum_app/src/core/consts/assets/assets.dart';
import 'package:optimum_app/src/core/consts/spaces/spaces.dart';
import 'package:optimum_app/src/core/consts/strings/consts.dart';
import 'package:optimum_app/src/core/extensions/extensions.dart';
import 'package:optimum_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:optimum_app/src/core/utils/nav.dart';

import '../../../domain/entities/the_media_entity.dart';
import '../../cubit/cubit.dart';
import '../../cubit/the_media_cubit.dart';
import '../the_media/the_media_page.dart';

part 'widgets/add_the_media_fields.dart';
part 'widgets/the_media_save_button.dart';

class AddTheMediaPage extends HookWidget {
  final TheMediaEntity? theMedia;

  static const routeName = '/hr/theMedia/add';

  const AddTheMediaPage({super.key, this.theMedia});

  @override
  Widget build(BuildContext context) {
    final local = AppLocalizations.of(context)!;

    //? Add Controllers to Map to reduce parameters passing
    final Map<String, TextEditingController> controllers = {
      Consts.nameAr: useTextEditingController(text: theMedia?.nameAr),
      Consts.nameEn: useTextEditingController(text: theMedia?.nameEn),
      Consts.notes: useTextEditingController(text: theMedia?.notes),
    };

    final isEdit = theMedia != null;

    return WillPopScope(
      onWillPop: () async {
        NV.nextScreenCloseOthersNamed(context, TheMediaPage.routeName);
        return false;
      },
      child: BlocProvider(
        create: (context) => TheMediaCubit.getTheMediaCubitSl(),
        child: BlocListener<TheMediaCubit, TheMediatate>(
            listener: (context, state) {
              log('theMediatate => ${state.runtimeType}');
              if (state is ErrorTheMediatate) {
                Navigator.pop(context);
                NV.nextScreenNamed(context, TheMediaPage.routeName);

                context.showBar(state.message.translateErrorMessage,
                    isError: true);
              } else if (state is SuccessTheMediatate) {
                Navigator.pop(context);
                NV.nextScreenCloseOthersNamed(context, TheMediaPage.routeName);

                context.showBar(state.message);
              }
            },
            child: Scaffold(
              appBar: AdaptiveTopBar(
                title: isEdit ? local.editMedia : local.addMedia,
              ),
              bottomNavigationBar: _TheMediaaveButton(
                controllers: controllers,
                theMedia: theMedia,
              ),
              body: Form(
                key: TheMediaCubit.formKey,
                child: _AddTheMediaFields(
                  controllers: controllers,
                  theMedia: theMedia,
                ),
              ),
            )),
      ),
    );
  }
}
