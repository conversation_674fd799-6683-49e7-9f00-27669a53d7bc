import 'dart:developer';

import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';
import 'package:optimum_app/src/core/data/remote/network/api_end_points.dart';
import 'package:optimum_app/src/core/data/remote/network/base_api_service.dart';

import '../models/training_courses_model.dart';

abstract class TrainingCoursesRemoteDataSource {
  Future<List<TrainingCourseModel>> getAllTrainingCourses(
      {String? columnFilters, int? perPage});

  Future<Unit> addTrainingCourse(TrainingCourseModel trainingCourse);

  Future<Unit> updateTrainingCourse(TrainingCourseModel trainingCourse);

  Future<Unit> deleteTrainingCourse(int id);
}

class TrainingCoursesRemoteDataSourceImpl
    extends TrainingCoursesRemoteDataSource {
  final BaseApiService _apiService;

  TrainingCoursesRemoteDataSourceImpl(this._apiService);

  @override
  Future<List<TrainingCourseModel>> getAllTrainingCourses(
      {String? columnFilters, int? perPage}) async {
    final endPoint =
        '${HRModuleURLs.trainingCourses}?columnFilters=$columnFilters&sortField=id&sortType=Desc&perPage=$perPage&page=1';
    try {
      final response = await _apiService.getResponse(
        endPoint,
      );

      final trainingCourses = compute(
        responseToTrainingCourses,
        response,
      );

      return trainingCourses;
    } catch (error) {
      log('TrainingCourseRemoteDataSourceImplError: $error');
      rethrow;
    }
  }

  @override
  Future<Unit> addTrainingCourse(TrainingCourseModel trainingCourse) async {
    const endPoint = HRModuleURLs.trainingCourses;
    try {
      log('TrainingCourseRemoteDataSourceImpl: ${trainingCourse.toJson()}');
      await _apiService.postResponse(
        endPoint,
        data: trainingCourse.toJson(),
      );

      return unit;
    } catch (error) {
      log('TrainingCourseRemoteDataSourceImplError: $error');
      rethrow;
    }
  }

  @override
  Future<Unit> updateTrainingCourse(TrainingCourseModel trainingCourse) async {
    final endPoint = '${HRModuleURLs.trainingCourses}/${trainingCourse.id}';

    try {
      await _apiService.putResponse(
        endPoint,
        data: trainingCourse.toJson(),
      );

      return unit;
    } catch (error) {
      log('TrainingCourseRemoteDataSourceImplError: $error');
      rethrow;
    }
  }

  @override
  Future<Unit> deleteTrainingCourse(int id) async {
    final endPoint = '${HRModuleURLs.trainingCourses}/$id';

    try {
      await _apiService.deleteResponse(
        endPoint,
      );

      return unit;
    } catch (error) {
      log('TrainingCourseRemoteDataSourceImplError: $error');
      rethrow;
    }
  }
}
