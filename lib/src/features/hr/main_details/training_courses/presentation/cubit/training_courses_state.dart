part of 'training_courses_cubit.dart';

abstract class TrainingCoursesState extends Equatable {
  const TrainingCoursesState();

  @override
  List<Object> get props => [];
}

class TrainingCoursesInitialState extends TrainingCoursesState {}

//? Get TrainingCourse States
class LoadingTrainingCoursesState extends TrainingCoursesState {}

class LoadedTrainingCoursesState extends TrainingCoursesState {
  final List<TrainingCourseEntity> trainingCourses;

  const LoadedTrainingCoursesState({required this.trainingCourses});

  @override
  List<Object> get props => [trainingCourses];
}

//? Add & Update & Delete TrainingCourse States
class LoadingAddUpdateDeleteTrainingCourseState extends TrainingCoursesState {}

class SuccessTrainingCoursesState extends TrainingCoursesState {
  final String message;

  const SuccessTrainingCoursesState({required this.message});

  @override
  List<Object> get props => [message];
}

//? Error TrainingCourse State
class ErrorTrainingCoursesState extends TrainingCoursesState {
  final String message;

  const ErrorTrainingCoursesState({required this.message});

  @override
  List<Object> get props => [message];
}
