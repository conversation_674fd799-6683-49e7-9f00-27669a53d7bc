part of add_regulations_page;

class _OvertimeTab extends HookWidget {
  final Map<String, TextEditingController> controllers;
  final RegulationEntity? regulation;

  const _OvertimeTab({Key? key, required this.controllers, this.regulation})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final local = AppLocalizations.of(context)!;
    Widget fields() => Column(
          children: [
            //! Working Days
            BaseTextField(
                textInputType: TextInputType.number,
                title: local.workingDays,
                controller: controllers[Consts.extraWorkingDays],
                hintText: local.workingDays,
                icon: const BaseLottieFieldIcon(
                  AnimatedAssets.fileField,
                )),

            context.fieldSpace,

            //! Weekend
            BaseTextField(
                textInputType: TextInputType.number,
                title: local.weekend,
                controller: controllers[Consts.extraWeekend],
                hintText: local.weekend,
                icon: const BaseLottieFieldIcon(
                  AnimatedAssets.fileField,
                )),

            context.fieldSpace,

            //! Official Vacations
            BaseTextField(
                textInputType: TextInputType.number,
                title: local.officialVacations,
                controller: controllers[Consts.extraOfficial],
                hintText: local.officialVacations,
                icon: const BaseLottieFieldIcon(
                  AnimatedAssets.fileField,
                )),
          ],
        );

    return ListView(
      padding: const EdgeInsets.all(AppSpaces.large),
      children: [
        fields(),
      ],
    );
  }
}
