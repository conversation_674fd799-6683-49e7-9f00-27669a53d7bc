import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:optimum_app/src/core/consts/strings/consts.dart';
import 'package:optimum_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:optimum_app/src/core/shared_widgets/tables/add_custom_table.dart';
import 'package:optimum_app/src/features/hr/main_details/salary_items/domain/entities/salary_items_entity.dart';
import 'package:optimum_app/src/features/hr/regulations/presentation/pages/add_regulation/widgets/tabs/regulation_violations_tab/add_table/main_add_violations_table.dart';

import '../../../../../../cubit/cubit.dart';

class AddRegulationTable extends StatelessWidget {
  final Function setState;
  final List<SalaryItemEntity>? salaryItems;

  const AddRegulationTable({
    Key? key,
    required this.setState,
    required this.salaryItems,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final local = AppLocalizations.of(context)!;

    List<String> titles = [
      local.violation,
      local.totalSalary,
      local.salaryItems,
      local.punishmentWay,
      local.firstTime,
      local.secondTime,
      local.thirdTime,
      local.fourAndMore,
      local.notes,
    ];

    List<String> punishmentWays = [
      "Static Value",
      "Fixed Percent",
    ];

    //? Cells ------------------------------
    List<Widget> cells(
        {required Map<String, ValueNotifier> tableFieldsControllers}) {
      return [
        //! Employee DropDown
        CellWidget(tableFieldsControllers[Consts.violations]?.value ?? ''),

        BaseTableCheckBox(
            valueNotifier: tableFieldsControllers[Consts.totalSalary]!
                as ValueNotifier<bool>,
            additionalOnChanged: (v) {
              setState(() {});
            }),

        IgnorePointer(
          ignoring: tableFieldsControllers[Consts.totalSalary]!.value,
          child: Stack(
            children: [
              SalaryItemsDropDown(
                selectedSalaryItem: tableFieldsControllers[Consts.salaryItem]!
                    as ValueNotifier<SalaryItemEntity?>,
                salaryItems: salaryItems,
                fromAddTable: true,
              ),
              if (tableFieldsControllers[Consts.totalSalary]!.value)
                Positioned.fill(
                  child: Container(
                      margin: const EdgeInsets.symmetric(vertical: 6),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        color: Colors.black.withOpacity(0.1),
                      )),
                ),
            ],
          ),
        ),

        BaseDropDown(
          onChanged: (value) {
            tableFieldsControllers[Consts.punishmentWay]?.value = value;
          },
          data: punishmentWays,
          selectedValue: tableFieldsControllers[Consts.punishmentWay]?.value,
          fromAddTable: true,
          label: local.punishmentWay,
        ),

        //? First Time
        BaseTableTextField(
            keyboardType: TextInputType.number,
            hintText: local.firstTime,
            isRequired: false,
            onChanged: (val) {
              tableFieldsControllers[Consts.firstTime]?.value = val;
            }),

        //? Second Time
        BaseTableTextField(
            keyboardType: TextInputType.number,
            hintText: local.secondTime,
            isRequired: false,
            onChanged: (val) {
              tableFieldsControllers[Consts.secondTime]?.value = val;
            }),

        //? Third Time
        BaseTableTextField(
            keyboardType: TextInputType.number,
            hintText: local.thirdTime,
            isRequired: false,
            onChanged: (val) {
              tableFieldsControllers[Consts.thirdTime]?.value = val;
            }),

        //? Fourth Time And More
        BaseTableTextField(
            keyboardType: TextInputType.number,
            hintText: local.fourAndMore,
            isRequired: false,
            onChanged: (val) {
              tableFieldsControllers[Consts.fourAndMore]?.value = val;
            }),

        //? Notes
        BaseTableTextField(
            keyboardType: TextInputType.text,
            hintText: local.notes,
            isRequired: false,
            onChanged: (val) {
              tableFieldsControllers[Consts.notes]?.value = val;
            }),
      ];
    }

    //? Columns ------------------------------
    final columnWidget = titles
        .map(
          (e) => CellWidget(
            e,
            isTitle: true,
            isWhite: true,
          ),
        )
        .toList(growable: false);

    //? Rows ------------------------------
    List<DataRow> rowWidget() {
      return regulationTableNotifiers
          .map((e) => DataRow(
                cells: cells(tableFieldsControllers: e)
                    .map((e) => DataCell(e))
                    .toList(),
              ))
          .toList(growable: false);
    }

    //? Table ------------------------------
    return BlocBuilder<RegulationsCubit, RegulationsState>(
      builder: (context, invoiceStates) {
        return AddCustomTable(
          columns: columnWidget,
          rows: rowWidget(),
        );
      },
    );
  }
}
