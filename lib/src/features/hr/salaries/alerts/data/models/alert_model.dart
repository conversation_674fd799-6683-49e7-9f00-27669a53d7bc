part of '../../alerts_imports.dart';

List<AlertModel> responseToAlerts(response){
  final alerts = List<AlertModel>.from(response['data']['data'].map((e)=>AlertModel.fromJson(e)));
  return alerts;
}
class AlertModel extends AlertEntity{
  const AlertModel({
    super.id,
    super.serial,
    super.branchId,
    super.annualId,
    super.processNo,
    super.employerId,
    super.departmentId,
    super.sectionId,
    super.alertTypeId,
    super.date,
    super.notes,
    super.createdAt,
    super.updatedAt,
    super.deletedAt,
    super.employer,
    super.department,
    super.section,
    super.alertType,
  });

  factory AlertModel.fromJson(Map<String, dynamic> json) => AlertModel(
    id: json["id"],
    serial: json["serial"],
    branchId: json["branch_id"],
    annualId: json["annual_id"],
    processNo: json["process_no"],
    employerId: json["employer_id"],
    departmentId: json["department_id"],
    sectionId: json["section_id"],
    alertTypeId: json["alert_type_id"],
    date: json["date"] == null ? null : DateTime.parse(json["date"]),
    notes: json["notes"],
    createdAt: json["created_at"] == null ? null : DateTime.parse(json["created_at"]),
    updatedAt: json["updated_at"] == null ? null : DateTime.parse(json["updated_at"]),
    deletedAt: json["deleted_at"],
    employer: json["employer"] == null ? null : EmployeeModel.fromJson(json["employer"]),
    department: json["department"] == null ? null : DepartmentModel.fromJson(json["department"]),
    section: json["section"] == null ? null : DepartmentModel.fromJson(json["section"]),
    alertType: json["alert_type"] == null ? null : AlertTypesModel.fromJson(json["alert_type"]),
  );

  Map<String, dynamic> toJson() => {
    "branch_id": branchId,
    "annual_id": annualId,
    "employer_id": employerId,
    "department_id": departmentId,
    "alert_type_id": alertTypeId,
    "date": date?.formatDateTimeToApi,
    "notes": notes,
  };
  Map<String, dynamic> toEditJson() => {
    "id": id,
    "serial": serial,
    "branch_id": branchId,
    "annual_id": annualId,
    "process_no": processNo,
    "employer_id": employerId,
    "department_id": departmentId,
    "section_id": sectionId,
    "alert_type_id": alertTypeId,
    "date": date?.formatDateTimeToApi,
    "notes": notes,
  };

  @override
  List<Object?> get props => [
    id,
    serial,
    branchId,
    annualId,
    processNo,
    employerId,
    departmentId,
    sectionId,
    alertTypeId,
    date,
    notes,
    createdAt,
    updatedAt,
    deletedAt,
    employer,
    department,
    section,
    alertType,
  ];
}
