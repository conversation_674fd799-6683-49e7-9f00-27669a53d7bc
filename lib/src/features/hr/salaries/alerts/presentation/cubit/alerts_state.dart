part of '../../alerts_imports.dart';

abstract class AlertsState extends Equatable {
  const AlertsState();

  @override
  List<Object> get props => [];
}

class AlertsInitialState extends AlertsState {}

//? Get Alert States
class LoadingAlertsState extends AlertsState {}

class LoadedAlertsState extends AlertsState {
  final List<AlertEntity> alerts;

  const LoadedAlertsState({required this.alerts});

  @override
  List<Object> get props => [alerts];
}

//? Add & Update & Delete Alert States
class LoadingAddUpdateDeleteAlertState extends AlertsState {}

class SuccessAlertsState extends AlertsState {
  final String message;

  const SuccessAlertsState({required this.message});

  @override
  List<Object> get props => [message];
}

//? Error Alert State
class ErrorAlertsState extends AlertsState {
  final String message;

  const ErrorAlertsState({required this.message});

  @override
  List<Object> get props => [message];
}
