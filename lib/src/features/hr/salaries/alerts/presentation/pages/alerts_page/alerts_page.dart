part of '../../../alerts_imports.dart';

class AlertsPage extends HookWidget {
  static const routeName = '/alerts/alerts';

  const AlertsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final local = AppLocalizations.of(context)!;

    return BlocProvider(
      create: (context) => AlertsCubit.getAlertsCubitSl()
        ..getAllAlerts(),
      child: GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),
        child: BlocListener<AlertsCubit, AlertsState>(
            listener: (context, state) {
              log('state: ${state.runtimeType}');
              if (state is ErrorAlertsState) {
                context.showBar(state.message.translateErrorMessage,
                    isError: true);
              } else if (state is SuccessAlertsState) {
                context.showBar(state.message);

                AlertsCubit.get(context).getAllAlerts();
              }
            },
            child: Scaffold(
                drawer: const AppDrawer(),
                appBar: AdaptiveTopBar(
                  title: local.alerts,
                  actions: [
                    BaseAddCircleButton(
                        onPressed: () => NV.nextScreenNamed(
                            context, AddAlertPage.routeName)),
                  ],
                ),
                body: const _AlertsTable())),
      ),
    );
  }
}
