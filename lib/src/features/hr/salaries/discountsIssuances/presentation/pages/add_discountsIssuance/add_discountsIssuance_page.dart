part of '../../../discountsIssuances_imports.dart';

final List<Map<String, ValueNotifier>> discountsIssuanceTableNotifiers = [];

class AddDiscountsIssuancePage extends HookWidget {
  final DiscountsIssuanceEntity? discountsIssuance;

  static const routeName = '/management/discountsIssuances/add';

  const AddDiscountsIssuancePage({
    super.key,
    this.discountsIssuance,
  });

  @override
  Widget build(BuildContext context) {
    final local = AppLocalizations.of(context)!;

    //? Add Controllers to Map to reduce parameters passing
    final TextEditingController noteController =
        useTextEditingController(text: discountsIssuance?.data?.notes);

    //? Add ValueNotifiers to Map to reduce parameters passing
    final Map<String, ValueNotifier> valueNotifiers = {
      Consts.department: useState<DepartmentEntity?>(null),
      Consts.section: useState<DepartmentEntity?>(null),
      Consts.employee: useState<EmployeeEntity?>(null),
      Consts.date: useState<DateTime?>(discountsIssuance?.data?.date),
    };

    final isEdit = discountsIssuance != null;
    dynamic discountsIssuancePageData;

    // useEffect(() {
    //   WidgetsBinding.instance.addPostFrameCallback((_) {
    //     previousDataTableNotifiers.clear();
    //     updatedDataTableNotifiers.clear();
    //     if(isEdit){
    //       if(discountsIssuance?.newSalaryItems != null){
    //         for (int i = 0; i < discountsIssuance!.newSalaryItems!.length; i++) {
    //           final newSalaryItem = discountsIssuance!.qnewSalaryItemslifications![i];
    //           final tableFields = {
    //             Consts.Item	: ValueNotifier<String?>(newSalaryItem.qualification),
    //             Consts.university: ValueNotifier<String?>(newSalaryItem.university),
    //             Consts.graduationYear: ValueNotifier<String?>(newSalaryItem.graduationYear),
    //             Consts.specialization: ValueNotifier<String?>(newSalaryItem.specialization),
    //           };
    //           updatedDataTableNotifiers.add(tableFields);
    //         }
    //       }else{
    //         final tableFields = {
    //           Consts.qualification: ValueNotifier<String?>(null),
    //           Consts.university: ValueNotifier<String?>(null),
    //           Consts.graduationYear: ValueNotifier<String?>(null),
    //           Consts.specialization: ValueNotifier<String?>(null),
    //         };
    //         updatedDataTableNotifiers.add(tableFields);
    //       }
    //
    //     }
    //   });
    //   return () {};
    // });

    return WillPopScope(
      onWillPop: () async {
        NV.nextScreenCloseOthersNamed(
            context, DiscountsIssuancesPage.routeName);

        return false;
      },
      child: MultiBlocProvider(
        providers: [
          BlocProvider(
            create: (context) =>
                EmployeesCubit.getEmployeesCubitSl()..getAllEmployees(),
          ),
          BlocProvider(
            create: (context) =>
                DepartmentsCubit.getDepartmentsCubitSl()..getAllDepartments(),
          ),
          BlocProvider(
            create: (context) =>
                EmployeesCubit.getEmployeesCubitSl()..getAllEmployees(),
          ),
          BlocProvider(
            create: (context) {
              if (isEdit) {
                return DiscountsIssuancesCubit.getDiscountsIssuanceCubitSl()
                  ..getEditDiscountsIssuancesPageData(
                      discountsIssuance!.data!.discountTypeId.toString());
              } else {
                return DiscountsIssuancesCubit.getDiscountsIssuanceCubitSl()
                  ..getDiscountsIssuancesPageData();
              }
            },
          ),
        ],
        child: BlocListener<DiscountsIssuancesCubit, DiscountsIssuancesState>(
          listener: (ctx, state) {
            log('DiscountsIssuanceState => ${state.runtimeType}');
            if (state is ErrorDiscountsIssuancesState) {
              NV.nextScreenCloseOthersNamed(
                  context, DiscountsIssuancesPage.routeName);

              context.showBar(state.message.translateErrorMessage,
                  isError: true);
            } else if (state is SuccessDiscountsIssuancesState) {
              NV.nextScreenCloseOthersNamed(
                  context, DiscountsIssuancesPage.routeName);

              context.showBar(state.message);
            } else if (state is LoadedDiscountsIssuancesPageDataState) {
              discountsIssuancePageData = state.discountsIssuancePageData;
            } else if (state is LoadedEditDiscountsIssuancesPageDataState) {
              discountsIssuancePageData = state.discountsIssuancePageData;
            }
          },
          child: Scaffold(
            appBar: AdaptiveTopBar(
              title: isEdit
                  ? local.editDiscountsIssuance
                  : local.addDiscountsIssuance,
            ),
            bottomNavigationBar: DiscountsIssuanceSaveButton(
              noteController: noteController,
              valueNotifiers: valueNotifiers,
              discountsIssuance: discountsIssuance,
              discountsIssuancePageData: discountsIssuancePageData,
            ),
            body: Form(
              key: DiscountsIssuancesCubit.formKey,
              child: Padding(
                padding: const EdgeInsets.all(AppSpaces.large),
                child: Column(
                  children: [
                    Flexible(
                      child: AddDiscountsIssuanceFields(
                        noteController: noteController,
                        valueNotifiers: valueNotifiers,
                        discountsIssuance: discountsIssuance,
                        discountsIssuancePageData: discountsIssuancePageData,
                      ),
                    ),
                    Expanded(
                      child: BlocBuilder<DiscountsIssuancesCubit,
                          DiscountsIssuancesState>(
                        builder: (ctx, state) {
                          log("this is discountsIssuance?.newSalaryItems ${discountsIssuance?.toString()}");
                          if (state is LoadedDiscountsIssuancesPageDataState ||
                              state
                                  is LoadedEditDiscountsIssuancesPageDataState) {
                            return _DiscountsIssuancesPage(
                              tableNotifiers: discountsIssuanceTableNotifiers,
                              discountsIssuances: discountsIssuance?.details,
                              allDiscountsIssuances: state
                                      is LoadedDiscountsIssuancesPageDataState
                                  ? state.discountsIssuancePageData.rewardTypes
                                  : state
                                          is LoadedDiscountsIssuancesPageDataState
                                      ? state
                                          .discountsIssuancePageData.rewardTypes
                                      : null,
                              isEdit: discountsIssuance != null,
                            );
                          } else {
                            return const LoadingWidget();
                          }
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
