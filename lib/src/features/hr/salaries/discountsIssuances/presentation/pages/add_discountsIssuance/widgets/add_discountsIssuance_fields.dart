part of '../../../../discountsIssuances_imports.dart';

class AddDiscountsIssuanceFields extends HookWidget {
  final TextEditingController noteController;
  final Map<String, ValueNotifier> valueNotifiers;
  final DiscountsIssuanceEntity? discountsIssuance;
  final EditDiscountsIssuancePageDataEntity? discountsIssuancePageData;

  const AddDiscountsIssuanceFields({
    Key? key,
    required this.noteController,
    required this.valueNotifiers,
    this.discountsIssuance,
    this.discountsIssuancePageData,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isEdit = discountsIssuance != null;

    return BlocBuilder<DiscountsIssuancesCubit, DiscountsIssuancesState>(
      builder: (context, state) {
        if (state is LoadingDiscountsIssuancesPageDataState ||
            state is LoadingAddUpdateDeleteDiscountsIssuanceState) {
          return const LoadingWidget(fieldLoading: true);
        }

        if (state is LoadedDiscountsIssuancesPageDataState ||
            state is LoadedEditDiscountsIssuancesPageDataState) {
          final discountsIssuancePageData = state is LoadedDiscountsIssuancesPageDataState
              ? state.discountsIssuancePageData
              : state is LoadedEditDiscountsIssuancesPageDataState
                  ? state.discountsIssuancePageData
                  : null;
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              //! Basic Details Fields
              DiscountsIssuanceDetailsFields(
                noteController: noteController,
                isEdit: isEdit,
                valueNotifiers: valueNotifiers,
                discountsIssuanceData: discountsIssuance,
                discountsIssuancePageData: discountsIssuancePageData,
              ),

              context.fieldSpace,
            ],
          );
        }

        return const SizedBox.shrink();
      },
    );
  }
}
