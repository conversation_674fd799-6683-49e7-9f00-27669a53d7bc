part of '../../../../../discountsIssuances_imports.dart';

class DiscountsIssuanceDetailsFields extends HookWidget {
  final  TextEditingController noteController;
  final Map<String, ValueNotifier> valueNotifiers;
  final dynamic discountsIssuancePageData;
  final DiscountsIssuanceEntity? discountsIssuanceData;
  final bool isEdit;

  const DiscountsIssuanceDetailsFields(
      {Key? key,
      required this.noteController,
      required this.isEdit,
      required this.valueNotifiers,
      required this.discountsIssuancePageData,
      required this.discountsIssuanceData})
      : super(key: key);
  @override
  Widget build(BuildContext context) {
    final isEdit = discountsIssuanceData != null;
    final local = AppLocalizations.of(context)!;

    final selectedDepartment =
        valueNotifiers[Consts.department] as ValueNotifier<DepartmentEntity?>;
    final selectedSection =
        valueNotifiers[Consts.section] as ValueNotifier<DepartmentEntity?>;
    final selectedEmployee =
    valueNotifiers[Consts.employee] as ValueNotifier<EmployeeEntity?>;

    final selectedDiscountsIssuanceDate =
    valueNotifiers[Consts.date] as ValueNotifier<DateTime?>;


    return Column(
      children: [

        //! Employee
        EmployeesDropDown(
          selectedEmployee: selectedEmployee,
          employeeId: discountsIssuanceData?.data == null? null:int.tryParse(discountsIssuanceData!.data!.employerId.toString()),
          label: local.employee,
        ),
        context.fieldSpace,

        // //! Departments TODO: remove comments symbols creating DepartmentsDropDown
        // DepartmentsDropDown(
        //   selectedDepartment: selectedDepartment,
        //   employeeId: selectedDepartment.value?.id,
        //   label: local.department,
        // ),
        // context.fieldSpace,

        // //! Employee
        // SectionsDropDown( TODO: remove comments symbols after creating SectionsDropDown and make it can affected by department
        //   selectedSection: selectedSection,
        //   sectionId: selectedSection.value?.id,
        //   label: local.section,
        // ),
        // context.fieldSpace,

        //! License Expiration
        ValueListenableBuilder(
          valueListenable: selectedDiscountsIssuanceDate,
          builder: (context, value, child) {
            return BaseDatePicker(
                isRequired: false,
                onChanged: (date) {
                  selectedDiscountsIssuanceDate.value = date!;
                },
                label: local.date,
                selectedDate: value);
          },
        ),
        context.fieldSpace,

      ],
    );
  }
}
