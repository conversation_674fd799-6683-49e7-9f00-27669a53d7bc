part of '../../employeeBorrows_imports.dart';

class EmployeeBorrows<PERSON>ubit extends Cubit<EmployeeBorrowsState> {
  final GetEmployeeBorrowsUseCase getAllEmployeeBorrowsUseCase;
  final GetEmployeeBorrowsDoneUseCase getAllEmployeeBorrowsDoneUseCase;
  final GetAcceptBorrowsOfDirectManagerUseCase
      getAcceptBorrowsOfDirectManagerUseCase;
  final GetAcceptBorrowsOfGeneralManagerUseCase
      getAcceptBorrowsOfGeneralManagerUseCase;
  final GetEmployeeBorrowsPageDataUseCase getEmployeeBorrowsPageDataUseCase;
  final GetEditEmployeeBorrowPageDataUseCase
      getEditEmployeeBorrowsPageDataUseCase;
  final AddEmployeeBorrowUseCase addEmployeeBorrowUseCase;
  final EditEmployeeBorrowUseCase editEmployeeBorrowUseCase;
  final DeleteEmployeeBorrowUseCase deleteEmployeeBorrowUseCase;
  final GeneralManagerAcceptEmployeeBorrowUseCase
      generalManagerAcceptEmployeeBorrowUseCase;
  final DirectManagerAcceptEmployeeBorrowUseCase
      directManagerAcceptEmployeeBorrowUseCase;

  EmployeeBorrowsCubit({
    required this.getAllEmployeeBorrowsUseCase,
    required this.getAllEmployeeBorrowsDoneUseCase,
    required this.getEmployeeBorrowsPageDataUseCase,
    required this.getEditEmployeeBorrowsPageDataUseCase,
    required this.getAcceptBorrowsOfDirectManagerUseCase,
    required this.getAcceptBorrowsOfGeneralManagerUseCase,
    required this.addEmployeeBorrowUseCase,
    required this.editEmployeeBorrowUseCase,
    required this.deleteEmployeeBorrowUseCase,
    required this.directManagerAcceptEmployeeBorrowUseCase,
    required this.generalManagerAcceptEmployeeBorrowUseCase,
  }) : super(EmployeeBorrowsInitialState());

  //? Get EmployeeBorrows Cubit
  static EmployeeBorrowsCubit get(context) => BlocProvider.of(context);

  //? Get EmployeeBorrows Dependency Injection Cubit
  static EmployeeBorrowsCubit getEmployeeBorrowCubitSl() =>
      sl<EmployeeBorrowsCubit>();

  //? Api Filters & Pagination
  final columnFilters = <String, String>{};

  final ValueNotifier<int> selectedPerPageEntries = ValueNotifier(10);

  //! Get EmployeeBorrows & EmployeeBorrows Page Data ----------------------------
  void getAllEmployeeBorrows() async {
    try {
      emit(LoadingEmployeeBorrowsState());

      final result = await getAllEmployeeBorrowsUseCase.call(
        columnFilters: jsonEncode(columnFilters),
        perPage: selectedPerPageEntries.value,
      );

      emit(_eitherGetEmployeeBorrowOrErrorState(result));
    } catch (e) {
      emit(ErrorEmployeeBorrowsState(
          message: e.toString().translateErrorMessage));
    }
  }

  void getAllEmployeeBorrowsDone() async {
    try {
      emit(LoadingEmployeeBorrowsState());

      final result = await getAllEmployeeBorrowsDoneUseCase.call(
        columnFilters: jsonEncode(columnFilters),
        perPage: selectedPerPageEntries.value,
      );

      emit(_eitherGetEmployeeBorrowOrErrorState(result));
    } catch (e) {
      emit(ErrorEmployeeBorrowsState(
          message: e.toString().translateErrorMessage));
    }
  }

  void getAcceptBorrowsOfDirectManager() async {
    try {
      emit(LoadingEmployeeBorrowsState());

      final result = await getAcceptBorrowsOfDirectManagerUseCase.call(
        columnFilters: jsonEncode(columnFilters),
        perPage: selectedPerPageEntries.value,
      );

      emit(_eitherGetEmployeeBorrowOrErrorState(result));
    } catch (e) {
      emit(ErrorEmployeeBorrowsState(
          message: e.toString().translateErrorMessage));
    }
  }

  void getAcceptBorrowsOfGeneralManager() async {
    try {
      emit(LoadingEmployeeBorrowsState());

      final result = await getAcceptBorrowsOfGeneralManagerUseCase.call(
        columnFilters: jsonEncode(columnFilters),
        perPage: selectedPerPageEntries.value,
      );

      emit(_eitherGetEmployeeBorrowOrErrorState(result));
    } catch (e) {
      emit(ErrorEmployeeBorrowsState(
          message: e.toString().translateErrorMessage));
    }
  }

  EmployeeBorrowsState _eitherGetEmployeeBorrowOrErrorState(
      Either<Failure, List<EmployeeBorrowEntity>> result) {
    return result.fold(
        (failure) => ErrorEmployeeBorrowsState(
            message: failure.toString().translateErrorMessage),
        (employeeBorrows) =>
            LoadedEmployeeBorrowsState(employeeBorrows: employeeBorrows));
  }

  void getEmployeeBorrowsPageData() async {
    try {
      emit(LoadingEmployeeBorrowsPageDataState());

      final result = await getEmployeeBorrowsPageDataUseCase();

      emit(_eitherGetEmployeeBorrowPageDataOrErrorState(result));
    } catch (e) {
      emit(ErrorEmployeeBorrowsState(
          message: e.toString().translateErrorMessage));
    }
  }

  EmployeeBorrowsState _eitherGetEmployeeBorrowPageDataOrErrorState(
    Either<Failure, EmployeeBorrowPageDataEntity> result,
  ) {
    return result.fold(
      (failure) => ErrorEmployeeBorrowsState(
          message: failure.toString().translateErrorMessage),
      (employeeBorrowPageData) => LoadedEmployeeBorrowsPageDataState(
          employeeBorrowPageData: employeeBorrowPageData),
    );
  }

  void getEditEmployeeBorrowsPageData(int id) async {
    try {
      emit(LoadingEditEmployeeBorrowsPageDataState());

      final result = await getEditEmployeeBorrowsPageDataUseCase(id.toString());
      emit(_eitherGetEditEmployeeBorrowPageDataOrErrorState(result));
    } catch (e) {
      emit(ErrorEmployeeBorrowsState(
          message: e.toString().translateErrorMessage));
    }
  }

  EmployeeBorrowsState _eitherGetEditEmployeeBorrowPageDataOrErrorState(
    Either<Failure, EditEmployeeBorrowPageDataEntity> result,
  ) {
    return result.fold(
      (failure) => ErrorEmployeeBorrowsState(
          message: failure.toString().translateErrorMessage),
      (employeeBorrowPageData) {
        return LoadedEditEmployeeBorrowsPageDataState(
            employeeBorrowPageData: employeeBorrowPageData);
      },
    );
  }

  //! Add & Update & Delete EmployeeBorrows ----------------------------

  //? Form Key for Validating Add Or Update EmployeeBorrow
  static GlobalKey<FormState> formKey = GlobalKey<FormState>();

  Future<void> addEmployeeBorrow(
    AppLocalizations local, {
    required Map<String, TextEditingController> controllers,
    required BuildContext context,
    required Map<String, ValueNotifier> valueNotifiers,
  }) async {
    try {
      emit(LoadingAddUpdateDeleteEmployeeBorrowState());

      // final detailList =
      //     List.generate(employeeBorrowTableNotifiers.length, (index) {
      //   Map<String, ValueNotifier> newSalaryObj =
      //   employeeBorrowTableNotifiers[index];
      //
      //   // return Detail(
      //   //   /// TODO: this is will be the model of details
      //   // );
      // });

      final employeeBorrowModel = EmployeeBorrowModel(
        branchId: sl<MainGeneralSettingsEntity>().selectedBranch?.id,
        annualId: sl<MainGeneralSettingsEntity>().selectedAnnual?.id,
        employerId: valueNotifiers[Consts.employee]?.value?.id,
        borrowingTypeId: valueNotifiers[Consts.borrowingType]?.value?.id,
        currancyId: valueNotifiers[Consts.currency]?.value?.id,
        salary: int.tryParse(controllers[Consts.salary]!.text),
        value: int.tryParse(controllers[Consts.paid]!.text),
        noOfMonths: int.tryParse(controllers[Consts.noOfMonths]!.text),
        valuePerMonth: int.tryParse(controllers[Consts.valuePerMonth]!.text),
        remain: int.tryParse(controllers[Consts.remain]!.text),
        startDate: valueNotifiers[Consts.startDate]?.value,
        date: valueNotifiers[Consts.date]?.value,
        debitAccountId: valueNotifiers[Consts.debitAccount]?.value.id,
        creditAccountId: valueNotifiers[Consts.creditAccount]?.value.id,
        directMangerId: valueNotifiers[Consts.employee]?.value?.directManager,
        notes: controllers[Consts.notes]!.text,
      );

      final result = await addEmployeeBorrowUseCase(employeeBorrowModel);

      emit(_eitherDoneMessageOrErrorState(result, local.addedSuccessfully));
    } catch (e) {
      emit(ErrorEmployeeBorrowsState(
          message: e.toString().translateErrorMessage));
    }
  }

  void editEmployeeBorrow(
    AppLocalizations local, {
    required EditEmployeeBorrowPageDataEntity? employeeBorrowPageData,
    required EmployeeBorrowEntity employeeBorrow,
    required Map<String, TextEditingController> controllers,
    required Map<String, ValueNotifier> valueNotifiers,
  }) async {
    try {
      emit(LoadingAddUpdateDeleteEmployeeBorrowState());

      // final detailList =
      //     List.generate(employeeBorrowTableNotifiers.length, (index) {
      //   Map<String, ValueNotifier> newSalaryObj =
      //   employeeBorrowTableNotifiers[index];
      //
      //   // return Detail(
      //   //   /// TODO: this is will be the model of details
      //   // );
      // });

      final employeeBorrowModel = EmployeeBorrowModel(
        id: employeeBorrow.id,
        serial: employeeBorrow.serial,
        processNo: employeeBorrow.processNo,
        branchId: sl<MainGeneralSettingsEntity>().selectedBranch?.id ??
            employeeBorrow.branchId,
        annualId: sl<MainGeneralSettingsEntity>().selectedAnnual?.id ??
            employeeBorrow.annualId,
        employerId: valueNotifiers[Consts.employee]?.value?.id ??
            employeeBorrow.employerId,
        borrowingTypeId: valueNotifiers[Consts.borrowingType]?.value?.id ??
            employeeBorrow.borrowingTypeId,
        currancyId: valueNotifiers[Consts.currency]?.value?.id ??
            employeeBorrow.currancyId,
        salary: int.tryParse(controllers[Consts.salary]!.text) ??
            employeeBorrow.salary,
        value: int.tryParse(controllers[Consts.paid]!.text) ??
            employeeBorrow.value,
        noOfMonths: int.tryParse(controllers[Consts.noOfMonths]!.text) ??
            employeeBorrow.noOfMonths,
        valuePerMonth: int.tryParse(controllers[Consts.valuePerMonth]!.text) ??
            employeeBorrow.valuePerMonth,
        remain: int.tryParse(controllers[Consts.remain]!.text) ??
            employeeBorrow.remain,
        startDate:
            valueNotifiers[Consts.startDate]?.value ?? employeeBorrow.startDate,
        date: valueNotifiers[Consts.date]?.value ?? employeeBorrow.date,
        debitAccountId: valueNotifiers[Consts.debitAccount]?.value.taxTypeId ??
            employeeBorrow.debitAccountId,
        creditAccountId:
            valueNotifiers[Consts.creditAccount]?.value.taxTypeId ??
                employeeBorrow.creditAccountId,
        directMangerId: valueNotifiers[Consts.employee]?.value?.directManager ??
            employeeBorrow.directMangerId,
        notes: controllers[Consts.notes]!.text != ''
            ? controllers[Consts.notes]!.text
            : employeeBorrow.notes,
      );

      final result = await editEmployeeBorrowUseCase(employeeBorrowModel);

      emit(_eitherDoneMessageOrErrorState(result, local.updatedSuccessfully));
    } catch (e) {
      emit(ErrorEmployeeBorrowsState(
          message: e.toString().translateErrorMessage));
    }
  }

  void deleteEmployeeBorrow(AppLocalizations local, int id) async {
    try {
      emit(LoadingAddUpdateDeleteEmployeeBorrowState());

      final result = await deleteEmployeeBorrowUseCase(id.toString());

      emit(_eitherDoneMessageOrErrorState(result, local.deletedSuccessfully));
    } catch (e) {
      emit(ErrorEmployeeBorrowsState(
          message: e.toString().translateErrorMessage));
    }
  }

  void directManagerAcceptEmployeeBorrow(AppLocalizations local, int id) async {
    try {
      emit(LoadingAddUpdateDeleteEmployeeBorrowState());

      final result =
          await directManagerAcceptEmployeeBorrowUseCase(id.toString());

      emit(_eitherDoneMessageOrErrorState(result, local.done));
    } catch (e) {
      emit(ErrorEmployeeBorrowsState(
          message: e.toString().translateErrorMessage));
    }
  }

  void generalManagerAcceptEmployeeBorrow(
      AppLocalizations local, int id) async {
    try {
      emit(LoadingAddUpdateDeleteEmployeeBorrowState());

      final result =
          await generalManagerAcceptEmployeeBorrowUseCase(id.toString());

      emit(_eitherDoneMessageOrErrorState(result, local.done));
    } catch (e) {
      emit(ErrorEmployeeBorrowsState(
          message: e.toString().translateErrorMessage));
    }
  }

  EmployeeBorrowsState _eitherDoneMessageOrErrorState(
    Either<Failure, Unit> failureOrDoneMessage,
    String message,
  ) {
    return failureOrDoneMessage.fold(
        (failure) => ErrorEmployeeBorrowsState(
            message: failure.toString().translateErrorMessage),
        (unit) => SuccessEmployeeBorrowsState(message: message));
  }
}
