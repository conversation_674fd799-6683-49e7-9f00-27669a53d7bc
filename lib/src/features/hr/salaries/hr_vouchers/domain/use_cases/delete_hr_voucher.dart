import 'package:dartz/dartz.dart';
import 'package:optimum_app/src/core/consts/error/failures.dart';
import 'package:optimum_app/src/features/hr/salaries/hr_vouchers/domain/repositories/hr_vouchers_repository.dart';

class DeleteHrVoucherUseCase {
  final HrVouchersRepository repository;

  DeleteHrVoucherUseCase(this.repository);

  Future<Either<Failure, Unit>> call(int id) async {
    return await repository.deleteHrVoucher(id);
  }
}
