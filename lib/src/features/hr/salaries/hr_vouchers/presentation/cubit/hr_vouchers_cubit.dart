import 'dart:convert';

import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:optimum_app/src/core/consts/error/failures.dart';
import 'package:optimum_app/src/core/consts/strings/consts.dart';
import 'package:optimum_app/src/core/extensions/extensions.dart';
import 'package:optimum_app/src/core/utils/injector/injector.dart';

import '../../domain/entities/hr_vouchers_vouchers_entity.dart';
import '../../domain/use_cases/add_hr_voucher.dart';
import '../../domain/use_cases/delete_hr_voucher.dart';
import '../../domain/use_cases/get_hr_vouchers.dart';
import '../../domain/use_cases/update_hr_voucher.dart';

part 'hr_vouchers_state.dart';

class HrVouchersCubit extends Cubit<HrVouchersState> {
  final GetHrVouchersUseCase getAllHrVouchersUseCase;
  final AddHrVoucherUseCase addHrVoucherUseCase;
  final UpdateHrVoucherUseCase updateHrVoucherUseCase;
  final DeleteHrVoucherUseCase deleteHrVoucherUseCase;

  HrVouchersCubit({
    required this.getAllHrVouchersUseCase,
    required this.addHrVoucherUseCase,
    required this.updateHrVoucherUseCase,
    required this.deleteHrVoucherUseCase,
  }) : super(HrVouchersInitialState());

  //? Get HrVouchers Cubit
  static HrVouchersCubit get(context) => BlocProvider.of(context);

  //? Get HrVouchers Dependency Injection Cubit
  static HrVouchersCubit getHrVouchersCubitSl() => sl<HrVouchersCubit>();

  //? Api Filters & Pagination
  final columnFilters = <String, String>{};

  final ValueNotifier<int> selectedPerPageEntries = ValueNotifier(10);

  //! Get HrVouchers ----------------------------

  void getAllHrVouchers() async {
    try {
      emit(LoadingHrVouchersState());

      final result = await getAllHrVouchersUseCase.call(
        columnFilters: jsonEncode(columnFilters),
        perPage: selectedPerPageEntries.value,
      );

      emit(_eitherGetHrVoucherOrErrorState(result));
    } catch (e) {
      emit(ErrorHrVouchersState(message: e.toString().translateErrorMessage));
    }
  }

  HrVouchersState _eitherGetHrVoucherOrErrorState(
      Either<Failure, List<HrVoucherEntity>> result) {
    return result.fold(
        (failure) => ErrorHrVouchersState(
            message: failure.toString().translateErrorMessage),
        (hrVouchers) => LoadedHrVouchersState(hrVouchers: hrVouchers));
  }

  //! Add & Update & Delete HrVouchers ----------------------------

  //? Form Key for Validating Add Or Update HrVoucher
  static GlobalKey<FormState> formKey = GlobalKey<FormState>();

  Future<void> addHrVoucher(AppLocalizations local,
      {required Map<String, TextEditingController> controllers}) async {
    try {
      emit(LoadingAddUpdateDeleteHrVoucherState());

      final hrVoucherEntity = HrVoucherEntity(
        nameAr: controllers[Consts.nameAr]!.text,
        nameEn: controllers[Consts.nameEn]!.text,
        notes: controllers[Consts.notes]!.text,
      );

      final result = await addHrVoucherUseCase(hrVoucherEntity);

      emit(_eitherDoneMessageOrErrorState(result, local.addedSuccessfully));
    } catch (e) {
      emit(ErrorHrVouchersState(message: e.toString().translateErrorMessage));
    }
  }

  void updateHrVoucher(AppLocalizations local,
      {required int id,
      required Map<String, TextEditingController> controllers}) async {
    try {
      emit(LoadingAddUpdateDeleteHrVoucherState());

      final hrVoucherEntity = HrVoucherEntity(
        id: id,
        nameAr: controllers[Consts.nameAr]!.text,
        nameEn: controllers[Consts.nameEn]!.text,
        notes: controllers[Consts.notes]!.text,
      );

      final result = await updateHrVoucherUseCase(hrVoucherEntity);

      emit(_eitherDoneMessageOrErrorState(result, local.updatedSuccessfully));
    } catch (e) {
      emit(ErrorHrVouchersState(message: e.toString().translateErrorMessage));
    }
  }

  void deleteHrVoucher(AppLocalizations local, {required int id}) async {
    try {
      emit(LoadingAddUpdateDeleteHrVoucherState());

      final result = await deleteHrVoucherUseCase(id);

      emit(_eitherDoneMessageOrErrorState(result, local.deletedSuccessfully));
    } catch (e) {
      emit(ErrorHrVouchersState(message: e.toString().translateErrorMessage));
    }
  }

  _eitherDoneMessageOrErrorState(
    Either<Failure, Unit> failureOrDoneMessage,
    String message,
  ) {
    return failureOrDoneMessage.fold(
        (failure) => ErrorHrVouchersState(
            message: failure.toString().translateErrorMessage),
        (unit) => SuccessHrVouchersState(message: message));
  }

  //? Search HrVouchers ----------------------------
  void onSearchChanged(
    String value, {
    required (String title, AppLocalizations local) filterKeyParams,
  }) {
    final filterKey = this.filterKey(filterKeyParams: filterKeyParams);

    columnFilters.addAll({filterKey: value});

    getAllHrVouchers();
  }

  String filterKey({
    required (String title, AppLocalizations local) filterKeyParams,
  }) {
    final title = filterKeyParams.$1;
    final local = filterKeyParams.$2;

    late String filterKey = '';
    if (title == local.nameAr) filterKey = 'name_ar';
    if (title == local.nameEn) filterKey = 'name_en';
    return filterKey;
  }
}
