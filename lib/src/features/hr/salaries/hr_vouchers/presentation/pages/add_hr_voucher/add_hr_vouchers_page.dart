import 'dart:core';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:optimum_app/src/core/adaptive_widgets/adaptive_top_bar.dart';
import 'package:optimum_app/src/core/consts/assets/assets.dart';
import 'package:optimum_app/src/core/consts/spaces/spaces.dart';
import 'package:optimum_app/src/core/consts/strings/consts.dart';
import 'package:optimum_app/src/core/extensions/extensions.dart';
import 'package:optimum_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:optimum_app/src/core/utils/nav.dart';
import 'package:optimum_app/src/features/hr/salaries/hr_vouchers/presentation/pages/hr_vouchers/hr_vouchers_page.dart';

import '../../../domain/entities/hr_vouchers_vouchers_entity.dart';
import '../../cubit/cubit.dart';

part 'widgets/add_hr_voucher_fields.dart';
part 'widgets/hr_voucher_save_button.dart';

class AddHrVoucherPage extends HookWidget {
  final HrVoucherEntity? hrVoucher;

  static const routeName = '/hr/hr_voucher/add';

  const AddHrVoucherPage({super.key, this.hrVoucher});

  @override
  Widget build(BuildContext context) {
    final local = AppLocalizations.of(context)!;

    //? Add Controllers to Map to reduce parameters passing
    final Map<String, TextEditingController> controllers = {
      Consts.nameAr: useTextEditingController(text: hrVoucher?.nameAr),
      Consts.nameEn: useTextEditingController(text: hrVoucher?.nameEn),
      Consts.notes: useTextEditingController(text: hrVoucher?.notes),
    };

    final isEdit = hrVoucher != null;

    return WillPopScope(
      onWillPop: () async {
        NV.nextScreenCloseOthersNamed(context, HrVouchersPage.routeName);
        return false;
      },
      child: BlocProvider(
        create: (context) => HrVouchersCubit.getHrVouchersCubitSl(),
        child: BlocListener<HrVouchersCubit, HrVouchersState>(
            listener: (context, state) {
              log('hrVoucherState => ${state.runtimeType}');
              if (state is ErrorHrVouchersState) {
                Navigator.pop(context);
                NV.nextScreenNamed(context, HrVouchersPage.routeName);

                context.showBar(state.message.translateErrorMessage,
                    isError: true);
              } else if (state is SuccessHrVouchersState) {
                Navigator.pop(context);
                NV.nextScreenCloseOthersNamed(
                    context, HrVouchersPage.routeName);

                context.showBar(state.message);
              }
            },
            child: Scaffold(
              appBar: AdaptiveTopBar(
                title: isEdit ? local.editHrVoucher : local.addHrVoucher,
              ),
              bottomNavigationBar: _HrVoucherSaveButton(
                controllers: controllers,
                hrVoucher: hrVoucher,
              ),
              body: Form(
                key: HrVouchersCubit.formKey,
                child: _AddHrVoucherFields(
                  controllers: controllers,
                  hrVoucher: hrVoucher,
                ),
              ),
            )),
      ),
    );
  }
}
