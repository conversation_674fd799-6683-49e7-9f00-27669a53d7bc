part of '../../rewardsIssuances_imports.dart';

abstract class RewardsIssuancesRemoteSource {
  Future<List<RewardsIssuanceModel>> getAllRewardsIssuances({
    String? columnFilters,
    int? perPage,
  });

  Future<RewardsIssuancePageDataModel> getRewardsIssuancePageData();

  Future<EditRewardsIssuancePageDataEntity> getEditRewardsIssuancePageData(String id);

  Future<Unit> addRewardsIssuance(RewardsIssuanceModel rewardsIssuance);

  Future<Unit> rewardsIssuance(RewardsIssuanceModel rewardsIssuance);

  Future<Unit> deleteRewardsIssuance(String id);
}

class RewardsIssuancesRemoteSourceImpl implements RewardsIssuancesRemoteSource {
  final NetworkApiService apiService;

  RewardsIssuancesRemoteSourceImpl(this.apiService);

  @override
  Future<List<RewardsIssuanceModel>> getAllRewardsIssuances({
    String? columnFilters,
    int? perPage,
  }) async {
    final branchId = sl<MainGeneralSettingsEntity>().selectedBranch?.id;

    final endPoint =
        '${HRModuleURLs.rewardsIssuances}?columnFilters=$columnFilters&sortField=id&sortType=Desc&perPage=$perPage&page=1&branch_id=$branchId';

    try {
      final response = await apiService.getResponse(endPoint);

      final rewardsIssuances = compute(responseToRewardsIssuances, response);

      return rewardsIssuances;
    } catch (error) {
      debugPrint('rewardsIssuanceRemoteDataSourceImplError: $error');
      rethrow;
    }
  }

  @override
  Future<Unit> addRewardsIssuance(RewardsIssuanceModel rewardsIssuance) async {
    try {
      final result = await apiService.postResponse(HRModuleURLs.rewardsIssuances,
          data: rewardsIssuance.toJson());
      log("messageeeeee: addRewardsIssuance result $result");
      return unit;
    } catch (error) {
      debugPrint('rewardsIssuanceRemoteDataSourceImplError: $error');
      rethrow;
    }
  }

  @override
  Future<Unit> deleteRewardsIssuance(String id) async {
    try {
      await apiService.deleteResponse("${HRModuleURLs.rewardsIssuances}/$id");
      return unit;
    } catch (error) {
      debugPrint('rewardsIssuanceRemoteDataSourceImplError: $error');
      rethrow;
    }
  }

  @override
  Future<Unit> rewardsIssuance(RewardsIssuanceModel rewardsIssuance) async {
    try {
      final result = await apiService.putResponse(
          "${HRModuleURLs.rewardsIssuances}/${rewardsIssuance.id}",
          data: rewardsIssuance.toJson());
      log("messageeeeee: rewardsIssuance result $result");

      return unit;
    } catch (error) {
      debugPrint('rewardsIssuanceRemoteDataSourceImplError: $error');
      rethrow;
    }
  }

  @override
  Future<RewardsIssuancePageDataModel> getRewardsIssuancePageData() async {
    try {
      final response = await apiService.getResponse(
        HRModuleURLs.rewardsIssuancesCreate,
      );
      final accounts = RewardsIssuancePageDataModel.fromJson(response['data']);

      return accounts;
    } catch (error) {
      debugPrint('rewardsIssuanceRemoteDataSourceImplError: $error');
      rethrow;
    }
  }

  @override
  Future<EditRewardsIssuancePageDataEntity> getEditRewardsIssuancePageData(String id) async {
    try {
      final response = await apiService.getResponse(
        "${HRModuleURLs.rewardsIssuances}/$id/edit",
      );
      final rewardsIssuancePageData =
          EditRewardsIssuancePageDataModel.fromJson(response['data']);

      return rewardsIssuancePageData;
    } catch (error) {
      debugPrint('rewardsIssuanceRemoteDataSourceImplError: $error');
      rethrow;
    }
  }

}
