part of '../../rewardsIssuances_imports.dart';

class RewardsIssuancesCubit extends Cubit<RewardsIssuancesState> {
  final GetRewardsIssuancesUseCase getAllRewardsIssuancesUseCase;
  final GetRewardsIssuancesPageDataUseCase getRewardsIssuancesPageDataUseCase;
  final GetEditRewardsIssuancePageDataUseCase
      getEditRewardsIssuancesPageDataUseCase;
  final AddRewardsIssuanceUseCase addRewardsIssuanceUseCase;
  final EditRewardsIssuanceUseCase editRewardsIssuanceUseCase;
  final DeleteRewardsIssuanceUseCase deleteRewardsIssuanceUseCase;

  RewardsIssuancesCubit({
    required this.getAllRewardsIssuancesUseCase,
    required this.getRewardsIssuancesPageDataUseCase,
    required this.getEditRewardsIssuancesPageDataUseCase,
    required this.addRewardsIssuanceUseCase,
    required this.editRewardsIssuanceUseCase,
    required this.deleteRewardsIssuanceUseCase,
  }) : super(RewardsIssuancesInitialState());

  //? Get RewardsIssuances Cubit
  static RewardsIssuancesCubit get(context) => BlocProvider.of(context);

  //? Get RewardsIssuances Dependency Injection Cubit
  static RewardsIssuancesCubit getRewardsIssuanceCubitSl() =>
      sl<RewardsIssuancesCubit>();

  //? Api Filters & Pagination
  final columnFilters = <String, String>{};

  final ValueNotifier<int> selectedPerPageEntries = ValueNotifier(10);

  //! Get RewardsIssuances & RewardsIssuances Page Data ----------------------------

  void getAllRewardsIssuances() async {
    try {
      emit(LoadingRewardsIssuancesState());
      final branchId = sl<MainGeneralSettingsEntity>().selectedBranch?.id;
      columnFilters.addAll({"branch_id": branchId.toString()});

      final result = await getAllRewardsIssuancesUseCase.call(
        columnFilters: jsonEncode(columnFilters),
        perPage: selectedPerPageEntries.value,
      );

      emit(_eitherGetRewardsIssuanceOrErrorState(result));
    } catch (e) {
      emit(ErrorRewardsIssuancesState(
          message: e.toString().translateErrorMessage));
    }
  }

  RewardsIssuancesState _eitherGetRewardsIssuanceOrErrorState(
      Either<Failure, List<RewardsIssuanceEntity>> result) {
    return result.fold(
        (failure) => ErrorRewardsIssuancesState(
            message: failure.toString().translateErrorMessage),
        (rewardsIssuances) =>
            LoadedRewardsIssuancesState(rewardsIssuances: rewardsIssuances));
  }

  void getRewardsIssuancesPageData() async {
    try {
      emit(LoadingRewardsIssuancesPageDataState());

      final result = await getRewardsIssuancesPageDataUseCase();

      emit(_eitherGetRewardsIssuancePageDataOrErrorState(result));
    } catch (e) {
      emit(ErrorRewardsIssuancesState(
          message: e.toString().translateErrorMessage));
    }
  }

  RewardsIssuancesState _eitherGetRewardsIssuancePageDataOrErrorState(
    Either<Failure, RewardsIssuancePageDataEntity> result,
  ) {
    return result.fold(
      (failure) => ErrorRewardsIssuancesState(
          message: failure.toString().translateErrorMessage),
      (rewardsIssuancePageData) => LoadedRewardsIssuancesPageDataState(
          rewardsIssuancePageData: rewardsIssuancePageData),
    );
  }

  void getEditRewardsIssuancesPageData(String id) async {
    try {
      emit(LoadingEditRewardsIssuancesPageDataState());

      final result = await getEditRewardsIssuancesPageDataUseCase(id);
      emit(_eitherGetEditRewardsIssuancePageDataOrErrorState(result));
    } catch (e) {
      emit(ErrorRewardsIssuancesState(
          message: e.toString().translateErrorMessage));
    }
  }

  RewardsIssuancesState _eitherGetEditRewardsIssuancePageDataOrErrorState(
    Either<Failure, EditRewardsIssuancePageDataEntity> result,
  ) {
    return result.fold(
      (failure) => ErrorRewardsIssuancesState(
          message: failure.toString().translateErrorMessage),
      (rewardsIssuancePageData) {
        return LoadedEditRewardsIssuancesPageDataState(
            rewardsIssuancePageData: rewardsIssuancePageData);
      },
    );
  }

  //! Add & Update & Delete RewardsIssuances ----------------------------

  //? Form Key for Validating Add Or Update RewardsIssuance
  static GlobalKey<FormState> formKey = GlobalKey<FormState>();

  Future<void> addRewardsIssuance(
    AppLocalizations local, {
    required TextEditingController noteController,
    required BuildContext context,
    required Map<String, ValueNotifier> valueNotifiers,
  }) async {
    emit(LoadingAddUpdateDeleteRewardsIssuanceState());

    final newSalaryList =
        List.generate(updatedDataTableNotifiers.length, (index) {
      Map<String, ValueNotifier> newSalaryObj =
          updatedDataTableNotifiers[index];

      return const SalaryItemEntity(

          /// TODO: fill Salary data here after editing its model
          );
    });

    final rewardsIssuanceModel = RewardsIssuanceModel(
      oldJobId: 1,
      newJobId: 2,
      date: valueNotifiers[Consts.date]?.value,
      newSalaryItems: newSalaryList,
      notes: noteController.text,
      employeeId: valueNotifiers[Consts.employee]?.value.id,
    );

    final result = await addRewardsIssuanceUseCase(rewardsIssuanceModel);

    emit(_eitherDoneMessageOrErrorState(result, local.addedSuccessfully));
    try {} catch (e) {
      emit(ErrorRewardsIssuancesState(
          message: e.toString().translateErrorMessage));
    }
  }

  void editRewardsIssuance(
    AppLocalizations local, {
    required EditRewardsIssuancePageDataEntity? rewardsIssuancePageData,
    required RewardsIssuanceEntity rewardsIssuance,
    required TextEditingController noteController,
    required Map<String, ValueNotifier> valueNotifiers,
  }) async {
    try {
      emit(LoadingAddUpdateDeleteRewardsIssuanceState());

      final newSalaryList =
          List.generate(updatedDataTableNotifiers.length, (index) {
        Map<String, ValueNotifier> newSalaryObj =
            updatedDataTableNotifiers[index];

        return const SalaryItemEntity(

            /// TODO: fill Salary data here after editing its model
            );
      });

      final rewardsIssuanceModel = RewardsIssuanceModel(
        oldJobId: 1,

        /// if null use rewardsIssuance.oldJobId
        newJobId: 2,

        /// if null use rewardsIssuance.newJobId
        date: valueNotifiers[Consts.date]?.value ?? rewardsIssuance.date,
        newSalaryItems: newSalaryList ?? rewardsIssuance.newSalaryItems,
        notes: noteController.text != ''
            ? noteController.text
            : rewardsIssuance.notes,
        employeeId: valueNotifiers[Consts.employee]?.value.taxTypeId ??
            rewardsIssuance.employeeId,
      );

      final result = await editRewardsIssuanceUseCase(rewardsIssuanceModel);

      emit(_eitherDoneMessageOrErrorState(result, local.updatedSuccessfully));
    } catch (e) {
      emit(ErrorRewardsIssuancesState(
          message: e.toString().translateErrorMessage));
    }
  }

  void deleteRewardsIssuance(AppLocalizations local, int id) async {
    try {
      emit(LoadingAddUpdateDeleteRewardsIssuanceState());

      final result = await deleteRewardsIssuanceUseCase(id.toString());

      emit(_eitherDoneMessageOrErrorState(result, local.deletedSuccessfully));
    } catch (e) {
      emit(ErrorRewardsIssuancesState(
          message: e.toString().translateErrorMessage));
    }
  }

  RewardsIssuancesState _eitherDoneMessageOrErrorState(
    Either<Failure, Unit> failureOrDoneMessage,
    String message,
  ) {
    return failureOrDoneMessage.fold(
        (failure) => ErrorRewardsIssuancesState(
            message: failure.toString().translateErrorMessage),
        (unit) => SuccessRewardsIssuancesState(message: message));
  }
}
