part of '../../../../../rewardsIssuances_imports.dart';

class RewardsIssuanceDetailsFields extends HookWidget {
  final  TextEditingController noteController;
  final Map<String, ValueNotifier> valueNotifiers;
  final dynamic rewardsIssuancePageData;
  final RewardsIssuanceEntity? rewardsIssuanceData;
  final bool isEdit;

  const RewardsIssuanceDetailsFields(
      {Key? key,
      required this.noteController,
      required this.isEdit,
      required this.valueNotifiers,
      required this.rewardsIssuancePageData,
      required this.rewardsIssuanceData})
      : super(key: key);
  @override
  Widget build(BuildContext context) {
    final isEdit = rewardsIssuanceData != null;
    final local = AppLocalizations.of(context)!;

    final selectedDepartment =
        valueNotifiers[Consts.department] as ValueNotifier<DepartmentEntity?>;
    final selectedSection =
        valueNotifiers[Consts.section] as ValueNotifier<DepartmentEntity?>;
    final selectedEmployee =
    valueNotifiers[Consts.employee] as ValueNotifier<EmployeeEntity?>;

    final selectedRewardsIssuanceDate =
    valueNotifiers[Consts.date] as ValueNotifier<DateTime?>;

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (isEdit) {
          selectedEmployee.value ??= rewardsIssuanceData!.employee;
        }
      });
      return () {};
    });

    return Column(
      children: [
        //! Employee
        EmployeesDropDown(
          selectedEmployee: selectedEmployee,
          employeeId: selectedEmployee.value?.id,
          label: local.employee,
        ),
        context.fieldSpace,
        // //! Departments TODO: remove comments symbols after creating DepartmentsDropDown
        // DepartmentsDropDown(
        //   selectedDepartment: selectedDepartment,
        //   employeeId: selectedDepartment.value?.id,
        //   label: local.department,
        // ),
        // context.fieldSpace,
        // //! Employee
        // SectionsDropDown( TODO: remove comments symbols after creating SectionsDropDown and make it can affected by department
        //   selectedSection: selectedSection,
        //   sectionId: selectedSection.value?.id,
        //   label: local.section,
        // ),
        // context.fieldSpace,

        //! License Expiration
        ValueListenableBuilder(
          valueListenable: selectedRewardsIssuanceDate,
          builder: (context, value, child) {
            return BaseDatePicker(
                isRequired: false,
                onChanged: (date) {
                  selectedRewardsIssuanceDate.value = date!;
                },
                label: local.date,
                selectedDate: value);
          },
        ),
        context.fieldSpace,

      ],
    );
  }
}
