import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:optimum_app/src/core/consts/strings/consts.dart';
import 'package:optimum_app/src/core/extensions/extensions.dart';
import 'package:optimum_app/src/features/hr/shifts/domain/entities/shifts_entity.dart';
import 'package:optimum_app/src/features/hr/shifts/presentation/pages/add_shift/add_shifts_page.dart';

import '../../../../../../../../core/shared_widgets/shared_widgets.dart';
import '../../../../../../../../core/shared_widgets/tables/add_custom_table.dart';
import '../../../../cubit/cubit.dart';

class AddShiftTable extends StatelessWidget {
  final ShiftEntity? shift;
  final setState;

  const AddShiftTable({
    Key? key,
    required this.shift,
    required this.setState,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final local = AppLocalizations.of(context)!;

    List<String> titles = [
      local.day,
      local.firstStart,
      local.firstEnd,
      local.secondStart,
      local.secondEnd,
      local.inMinutes,
      local.minutes,
    ];

    //? Cells ------------------------------
    List<Widget> cells(
        {required Map<String, ValueNotifier> tableFieldsControllers}) {
      final inMinutes = tableFieldsControllers[Consts.inMinutes]!.value;

      final firstStart = tableFieldsControllers[Consts.firstStart]!
          .value
          .toString()
          .convertTimeToMinutes();

      final firstEnd = tableFieldsControllers[Consts.firstEnd]!
          .value
          .toString()
          .convertTimeToMinutes();

      final secondStart = tableFieldsControllers[Consts.secondStart]!
          .value
          .toString()
          .convertTimeToMinutes();

      final secondEnd = tableFieldsControllers[Consts.secondEnd]!
          .value
          .toString()
          .convertTimeToMinutes();

      final minutes = tableFieldsControllers[Consts.minutes];

      if (inMinutes == true) {
        final firstShift = (firstStart - firstEnd).abs();

        final secondShift = (secondStart - secondEnd).abs();

        final firstShiftInMinutes = firstShift > 0 ? firstShift : 0;

        final secondShiftInMinutes = secondShift > 0 ? secondShift : 0;

        minutes?.value =
            (firstShiftInMinutes + secondShiftInMinutes).toString();
      } else {
        minutes?.value = null;
      }

      return [
        CellWidget(tableFieldsControllers[Consts.day]!.value.toString()),
        BaseTimePicker(
          fromAddTable: true,
          selectedTime: tableFieldsControllers[Consts.firstStart]
              as ValueNotifier<String?>,
          additionalOnChanged: (val) {
            setState(() {});
          },
        ),
        BaseTimePicker(
          fromAddTable: true,
          selectedTime:
              tableFieldsControllers[Consts.firstEnd] as ValueNotifier<String?>,
          additionalOnChanged: (val) {
            setState(() {});
          },
        ),
        BaseTimePicker(
          fromAddTable: true,
          selectedTime: tableFieldsControllers[Consts.secondStart]
              as ValueNotifier<String?>,
          additionalOnChanged: (val) {
            setState(() {});
          },
        ),
        BaseTimePicker(
          fromAddTable: true,
          selectedTime: tableFieldsControllers[Consts.secondEnd]
              as ValueNotifier<String?>,
          additionalOnChanged: (val) {
            setState(() {});
          },
        ),
        BaseTableCheckBox(
            valueNotifier: tableFieldsControllers[Consts.inMinutes]
                as ValueNotifier<bool?>,
            additionalOnChanged: (val) {
              setState(() {});
            }),
        CellWidget(minutes?.value?.toString() ?? '0')
      ];
    }

    //? Columns ------------------------------
    final columnWidget = titles
        .map(
          (e) => CellWidget(
            e,
            isTitle: true,
            isWhite: true,
          ),
        )
        .toList(growable: false);

    //? Rows ------------------------------
    List<DataRow> rowWidget() {
      return shiftTableNotifiers
          .map((e) => DataRow(
                cells: cells(tableFieldsControllers: e)
                    .map((e) => DataCell(e))
                    .toList(),
              ))
          .toList(growable: false);
    }

    //? Table ------------------------------
    return BlocBuilder<ShiftsCubit, ShiftsState>(
      builder: (context, invoiceStates) {
        return AddCustomTable(
          columns: columnWidget,
          rows: rowWidget(),
        );
      },
    );
  }
}
