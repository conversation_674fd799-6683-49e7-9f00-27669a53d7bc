import 'dart:developer';

import 'package:optimum_app/src/core/data/remote/network/api_end_points.dart';
import 'package:optimum_app/src/core/data/remote/network/base_api_service.dart';

import '../models/eligible_employees_model.dart';

abstract class EligibleEmployeesRemoteDataSource {
  Future<List<EligibleEmployeesModel>> getAllEligibleEmployees(
      {required int sectionId});
}

class EligibleEmployeesRemoteDataSourceImpl
    extends EligibleEmployeesRemoteDataSource {
  final BaseApiService apiService;

  EligibleEmployeesRemoteDataSourceImpl({required this.apiService});

  @override
  Future<List<EligibleEmployeesModel>> getAllEligibleEmployees(
      {required int sectionId}) async {
    final endPoint = '${HRModuleURLs.eligibleEmployeesReports}/$sectionId';

    try {
      final response = await apiService.getResponse(
        endPoint,
      );

      log('EligibleEmployeesRemoteDataSourceImplUrl: $endPoint');
      final eligibleEmployees = List<EligibleEmployeesModel>.from(
          response['data']['employees']
              .map((x) => EligibleEmployeesModel.fromJson(x)));

      return eligibleEmployees;
    } catch (error) {
      log('EligibleEmployeesRemoteDataSourceImplError: $error');
      rethrow;
    }
  }
}
