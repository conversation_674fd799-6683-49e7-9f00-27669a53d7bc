import 'package:optimum_app/src/core/consts/strings/config.dart';
import 'package:optimum_app/src/core/data/remote/response/api_strings.dart';
import 'package:optimum_app/src/core/utils/injector/injector.dart';

import '../../domain/entities/eligible_employees_entity.dart';

class EligibleEmployeesModel extends EligibleEmployeesEntity {
  const EligibleEmployeesModel({
    super.nameAr,
    super.nameEn,
    super.id,
    super.phone,
    super.name,
    super.dateOfHiring,
    super.avatar,
    super.gender,
  });

  factory EligibleEmployeesModel.fromJson(Map<String, dynamic> json) {
    return EligibleEmployeesModel(
        nameAr: json['name_ar'] ?? '',
        nameEn: json['name_en'] ?? '',
        id: json['id'],
        phone: json['phone'] ?? '',
        name: json['name'] ?? '',
        dateOfHiring: DateTime.parse(json['date_of_hiring'] ?? ''),
        avatar: ApiStrings.baseUrl(sl<ConfigData>().subDomain) +
            (json['avater'] ?? ''),
        gender: json['gender'] ?? '');
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['name_ar'] = nameAr;
    data['name_en'] = nameEn;
    data['id'] = id;
    data['phone'] = phone;
    data['name'] = name;
    return data;
  }
}
