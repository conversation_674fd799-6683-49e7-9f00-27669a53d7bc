part of '../../../vacation_allowance_settings.dart';

class _EligibleVoucherTab extends HookWidget {
  final VacationAllowancesEntity vacationAllowancesEntity;
  final Map<String, ValueNotifier> valueNotifiers;

  const _EligibleVoucherTab({
    Key? key,
    required this.valueNotifiers,
    required this.vacationAllowancesEntity,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListView(
      children: [
        BlocBuilder<AccountTreesCubit, AccountsTreesState>(
          builder: (context, state) {
            if (state is LoadingAccountsTreesState) {
              return const LoadingWidget(fieldLoading: true);
            } else if (state is LoadedAccountsTreesState) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  //? Active Tile
                  BaseSingleCheckTile(
                    title: context.local.active,
                    onChanged: (value) {
                      valueNotifiers[Consts.isActive]!.value = value;
                    },
                    value: valueNotifiers[Consts.isActive]!.value,
                  ),

                  context.fieldSpace,
                  //? From Account
                  // BaseAccountDropDown(
                  //   selectedAccount: valueNotifiers[Consts.fromAccount]!
                  //       as ValueNotifier<AccountTreeEntity?>,
                  //   label: context.local.fromAccount,
                  //   accountId:
                  //       vacationAllowancesEntity.accountSettings?.rewardsDebit,
                  // ),
                  context.fieldSpace,

                  //? To Account
                  // BaseAccountDropDown(
                  //   selectedAccount: valueNotifiers[Consts.toAccount]!
                  //       as ValueNotifier<AccountTreeEntity?>,
                  //   label: context.local.toAccount,
                  //   accountId:
                  //       vacationAllowancesEntity.accountSettings?.rewardsCredit,
                  // ),
                ],
              );
            }
            return const SizedBox.shrink();
          },
        ),
      ],
    );
  }
}
