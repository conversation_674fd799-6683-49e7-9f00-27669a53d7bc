import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:optimum_app/src/core/adaptive_widgets/adaptive_top_bar.dart';
import 'package:optimum_app/src/core/extensions/extensions.dart';
import 'package:optimum_app/src/core/shared_widgets/dialogs/platform_dialog.dart';
import 'package:optimum_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:optimum_app/src/core/shared_widgets/tables/custom_table.dart';
import 'package:optimum_app/src/core/utils/nav.dart';
import 'package:optimum_app/src/features/home/<USER>/drawer/app_drawer.dart';

import '../../../domain/entities/violations_entity.dart';
import '../../cubit/cubit.dart';
import '../add_violations/add_violations_page.dart';

part 'widgets/violations_table.dart';

class ViolationsPage extends HookWidget {
  static const routeName = '/hr/violationss';

  const ViolationsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final local = AppLocalizations.of(context)!;

    return BlocProvider(
      create: (context) =>
          ViolationsCubit.getViolationsCubitSl()..getAllViolations(),
      child: GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),
        child: BlocListener<ViolationsCubit, ViolationsState>(
            listener: (context, state) {
              log('state: ${state.runtimeType}');
              if (state is ErrorViolationsState) {
                context.showBar(state.message.translateErrorMessage,
                    isError: true);
              } else if (state is SuccessViolationsState) {
                context.showBar(state.message);

                ViolationsCubit.get(context).getAllViolations();
              }
            },
            child: Scaffold(
                drawer: const AppDrawer(),
                appBar: AdaptiveTopBar(
                  title: local.violations,
                  actions: [
                    BaseAddCircleButton(
                        onPressed: () => NV.nextScreenNamed(
                            context, AddViolationPage.routeName)),
                  ],
                ),
                body: const _ViolationsTable())),
      ),
    );
  }
}
