part of '../violations_page.dart';

class _ViolationsTable extends StatelessWidget {
  const _ViolationsTable({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final local = AppLocalizations.of(context)!;

    List<String> titles = [
      local.code,
      local.nameAr,
      local.nameEn,
      local.type,
      local.notes,
      local.actions
    ];

    List<Widget> cells({
      required ViolationEntity violation,
    }) {
      return [
        CellWidget(violation.id!.toString()),
        CellWidget(violation.nameAr!),
        CellWidget(violation.nameEn!),
        CellWidget(violation.type!),
        CellWidget(violation.notes!),
        ActionButtons(
            onEdit: () =>
                NV.nextScreen(context, AddViolationPage(violation: violation)),
            onDelete: () => showPlatformDialog(
                  context,
                  isDelete: true,
                  title: local.deleteViolation,
                  content: local.deleteViolationMessage,
                  action: () => context
                      .read<ViolationsCubit>()
                      .deleteViolation(local, id: violation.id!),
                )),
      ];
    }

    return _Table(
      titles: titles,
      cells: cells,
    );
  }
}

class _Table extends HookWidget {
  final List<String> titles;
  final List<Widget> Function({required ViolationEntity violation}) cells;

  const _Table({Key? key, required this.titles, required this.cells})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ViolationsCubit, ViolationsState>(
        builder: (context, violationsState) {
      if (violationsState is ErrorViolationsState) {
        return Center(
          child: Text(violationsState.message.translateErrorMessage),
        );
      }

      final showFooter = violationsState is LoadedViolationsState &&
          violationsState.violations.isNotEmpty;

      //! Titles Row
      final columnWidget = titles
          .map(
            (e) => CellWidget(e, isTitle: true),
          )
          .toList(growable: false);

      //! Data Rows
      List<DataRow> rowWidget() {
        if (violationsState is LoadingViolationsState ||
            violationsState is LoadingAddUpdateDeleteViolationState) {
          return LoadingWidget.loadingTable(titles.length);
        } else if (violationsState is LoadedViolationsState) {
          return violationsState.violations
              .map((e) => DataRow(
                    cells: cells(violation: e).map((e) => DataCell(e)).toList(),
                  ))
              .toList(growable: false);
        }
        return [];
      }

      //! Table Body
      Widget tableBody() {
        void onPerPageChanged(value) {
          ViolationsCubit.get(context).selectedPerPageEntries.value = value;
          ViolationsCubit.get(context).getAllViolations();
        }

        return CustomTable(
          selectedPerPageEntriesValue:
              ViolationsCubit.get(context).selectedPerPageEntries,
          onChangePerPage: onPerPageChanged,
          showFooter: showFooter,
          columns: columnWidget,
          rows: [
            //? Search Fields Row
            DataRow(
              selected: true,
              cells: titles
                  .map((e) => DataCell(
                        _SearchRow(title: e),
                      ))
                  .toList(growable: false),
            ),

            //? Data Rows
            ...rowWidget(),
          ],
        );
      }

      return Stack(
        children: [
          tableBody(),
          if (violationsState is LoadedViolationsState &&
              violationsState.violations.isEmpty)
            const Center(
              child: EmptyLottieIcon(),
            )
        ],
      );
    });
  }
}

class _SearchRow extends HookWidget {
  final String title;

  const _SearchRow({Key? key, required this.title}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final local = AppLocalizations.of(context)!;

    if (title != local.notes && title != local.actions) {
      return BaseTableTextField(
          hintText: title,
          onChanged: (value) => ViolationsCubit.get(context)
              .onSearchChanged(value, filterKeyParams: (title, local)));
    }

    return const SizedBox();
  }
}
