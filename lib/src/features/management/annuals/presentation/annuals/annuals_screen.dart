import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:optimum_app/src/core/adaptive_widgets/adaptive_top_bar.dart';
import 'package:optimum_app/src/core/extensions/extensions.dart';
import 'package:optimum_app/src/features/home/<USER>/drawer/app_drawer.dart';

import '../../../../../core/shared_widgets/shared_widgets.dart';
import '../../../../../core/utils/nav.dart';
import '../add_annual/add_annual_page.dart';
import '../cubit/annuals_cubit.dart';
import '../cubit/annuals_state.dart';
import 'widgets/annuals_table.dart';

class AnnualsScreen extends HookWidget {
  static const routeName = '/management/annuals';

  const AnnualsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final local = AppLocalizations.of(context)!;

    return BlocProvider(
      create: (context) => AnnualsCubit.getAnnualsCubitSl()..getAllAnnuals(),
      child: GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),
        child: BlocListener<AnnualsCubit, AnnualsState>(
            listener: (context, state) {
              log('state: ${state.runtimeType}');
              if (state is ErrorAnnualsState) {
                context.showBar(state.message.translateErrorMessage,
                    isError: true);
              } else if (state is SuccessAnnualsState) {
                context.showBar(state.message);

                AnnualsCubit.get(context).getAllAnnuals();
              }
            },
            child: Scaffold(
                drawer: const AppDrawer(),
                appBar: AdaptiveTopBar(
                  title: local.annuals,
                  actions: [
                    BaseAddCircleButton(
                        onPressed: () => NV.nextScreenNamed(
                            context, AddAnnualPage.routeName)),
                  ],
                ),
                body: const AnnualsTable())),
      ),
    );
  }
}
