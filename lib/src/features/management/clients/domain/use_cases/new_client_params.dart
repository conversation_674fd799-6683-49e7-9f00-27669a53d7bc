// ignore_for_file: public_member_api_docs, sort_constructors_first

part of '../../clients.dart';

class NewClientParams extends Equatable {
  final String? nameAr;
  final String? nameEn;
  final String? taxNumber;
  final String? commericalRegistrationNo;
  final String? registrationExpireDate;
  final String? status;
  final String? email;
  final String? phone;
  final String? phone2;
  final String? fax;
  final List? subClients;
  final List? clientContacts;
  final List? clientWareHouses;
  final List? clientAuthourizations;
  final String? website;
  final String? countryId;
  final String? stateId;
  final String? city;
  final String? address;
  final String? notes;
  final String? creditLimit;
  final String? creditDays;
  final bool? createAccount;

  const NewClientParams(
      {this.nameAr,
      this.nameEn,
      this.address,
      this.email,
      this.city,
      this.phone,
      this.phone2,
      this.taxNumber,
      this.clientAuthourizations,
      this.clientContacts,
      this.clientWareHouses,
      this.commericalRegistrationNo,
      this.countryId,
      this.createAccount,
      this.creditDays,
      this.creditLimit,
      this.fax,
      this.notes,
      this.registrationExpireDate,
      this.stateId,
      this.status,
      this.subClients,
      this.website});

  @override
  List<Object?> get props => [
        nameAr,
        nameEn,
        address,
        email,
        city,
        phone,
        phone2,
        taxNumber,
        clientAuthourizations,
        clientContacts,
        clientWareHouses,
        commericalRegistrationNo,
        countryId,
        createAccount,
        creditDays,
        creditLimit,
        fax,
        notes,
        registrationExpireDate,
        stateId,
        status,
        subClients,
        website
      ];

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'name_ar': nameAr,
      'name_en': nameEn,
      'tax_number': taxNumber,
      'commerical_registration_no': commericalRegistrationNo,
      'registration_expire_date': registrationExpireDate,
      'status': status,
      'email': email,
      'phone': phone,
      'phone2': phone2,
      'fax': fax,
      'sub_clients': json.encode(subClients),
      'client_contacts': json.encode(clientContacts),
      'client_wareHouses': json.encode(clientWareHouses),
      'client_authourizations': json.encode(clientAuthourizations),
      'website': website,
      'country_id': countryId,
      'state_id': stateId,
      'city': city,
      'address': address,
      'notes': notes,
      'credit_limit': creditLimit,
      'credit_days': creditDays,
      'create_account': createAccount,
    };
  }

  factory NewClientParams.fromMap(Map<String, dynamic> map) {
    return NewClientParams(
      nameAr: map['nameAr'] != null ? map['nameAr'] as String : null,
      nameEn: map['nameEn'] != null ? map['nameEn'] as String : null,
      taxNumber: map['taxNumber'] != null ? map['taxNumber'] as String : null,
      commericalRegistrationNo: map['commericalRegistrationNo'] != null
          ? map['commericalRegistrationNo'] as String
          : null,
      registrationExpireDate: map['registrationExpireDate'] != null
          ? map['registrationExpireDate'] as String
          : null,
      status: map['status'] != null ? map['status'] as String : null,
      email: map['email'] != null ? map['email'] as String : null,
      phone: map['phone'] != null ? map['phone'] as String : null,
      phone2: map['phone2'] != null ? map['phone2'] as String : null,
      fax: map['fax'] != null ? map['fax'] as String : null,
      subClients: map['subClients'],
      clientContacts: map['clientContacts'],
      clientWareHouses: map['clientWareHouses'],
      clientAuthourizations: map['clientAuthourizations'],
      website: map['website'] != null ? map['website'] as String : null,
      countryId: map['countryId'] != null ? map['countryId'] as String : null,
      stateId: map['stateId'] != null ? map['stateId'] as String : null,
      city: map['city'] != null ? map['city'] as String : null,
      address: map['address'] != null ? map['address'] as String : null,
      notes: map['notes'] != null ? map['notes'] as String : null,
      creditLimit:
          map['creditLimit'] != null ? map['creditLimit'] as String : null,
      creditDays:
          map['creditDays'] != null ? map['creditDays'] as String : null,
      createAccount:
          map['createAccount'] != null ? map['createAccount'] as bool : null,
    );
  }

  String toJson() => json.encode(toMap());

  factory NewClientParams.fromJson(String source) =>
      NewClientParams.fromMap(json.decode(source) as Map<String, dynamic>);
}
