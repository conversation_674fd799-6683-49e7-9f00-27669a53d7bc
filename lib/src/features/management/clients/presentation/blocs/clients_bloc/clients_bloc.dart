import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:optimum_app/src/core/consts/error/failures.dart';
import 'package:optimum_app/src/core/extensions/extensions.dart';
import 'package:optimum_app/src/core/local_models/use_case.dart';
import 'package:optimum_app/src/features/management/clients/clients.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'clients_event.dart';
part 'clients_state.dart';

const String SERVER_FAILURE_MESSAGE = 'Server Failure';
const String CACHE_FAILURE_MESSAGE = 'Cache Failure';
const String INVALID_INPUT_FAILURE_MESSAGE =
    'Invalid Input - The number must be a positive integer or zero.';

class ClientsBloc extends Bloc<ClientsEvent, ClientsState> {
  final SharedPreferences sharedPreferences;
  final GetClientsUC getClientsUC;
  final GetAllClientsUC getAllClientsUC;

  final AddClientUC addClientUC;
  final EditClientUC editClientUC;
  final DeleteClientUC deleteClientUC;

  ClientsBloc(
      {required this.sharedPreferences,
      required this.getClientsUC,
      required this.addClientUC,
      required this.editClientUC,
      required this.deleteClientUC,
      required this.getAllClientsUC})
      : super(const ClientsInitial()) {
    on<GetClientsEvent>(_getClients);
    on<GetAllClientsEvent>(_getAllClients);
    on<AddClientEvent>(_addClient);
    on<EditClientEvent>(_editClient);
    on<DeleteClientEvent>(_deleteClient);
  }
  FutureOr<void> _getClients(
      GetClientsEvent event, Emitter<ClientsState> emit) async {
    emit(const ClientsLoading());

    try {
      final admin = await getClientsUC.call(QueryParams(
          columnFilters: event.columnFilters,
          sortType: event.desc,
          page: event.page,
          perPage: event.perPage,
          sortField: event.sortField));
      emit(admin.fold((l) => ClientsLoadFailed(_mapFailureToMessage(l)), (r) {
        return ClientsLoadSuccess(r as ClientsModel);
      }));
    } on Exception catch (e) {
      emit(ClientsLoadFailed(e.toString().translateErrorMessage));
    }
  }

  FutureOr<void> _getAllClients(
      GetAllClientsEvent event, Emitter<ClientsState> emit) async {
    emit(const ClientsLoading());

    try {
      final admin = await getAllClientsUC.call(NoParams());
      emit(admin.fold((l) => ClientsLoadFailed(_mapFailureToMessage(l)), (r) {
        return AllClientsLoaded(r as AllClientsModel);
      }));
    } on Exception catch (e) {
      emit(ClientsLoadFailed(e.toString().translateErrorMessage));
    }
  }

  FutureOr<void> _addClient(
      AddClientEvent event, Emitter<ClientsState> emit) async {
    emit(const ClientsLoading());
    try {
      final client = await addClientUC.call(event.newClient);
      emit(client.fold((l) => ClientsLoadFailed(_mapFailureToMessage(l)), (r) {
        return ClientsModifiedState(r);
      }));
    } on Exception catch (e) {
      emit(ClientsLoadFailed(e.toString().translateErrorMessage));
    }
  }

  FutureOr<void> _editClient(
      EditClientEvent event, Emitter<ClientsState> emit) async {
    emit(const ClientsLoading());

    try {
      final client = await editClientUC.call(event.newClient, event.clientId);
      emit(client.fold((l) => ClientsLoadFailed(_mapFailureToMessage(l)), (r) {
        return ClientsModifiedState(r);
      }));
    } on Exception catch (e) {
      emit(ClientsLoadFailed(e.toString().translateErrorMessage));
    }
  }

  FutureOr<void> _deleteClient(
      DeleteClientEvent event, Emitter<ClientsState> emit) async {
    emit(const ClientsLoading());

    try {
      final client = await deleteClientUC.call(event.clientId);
      emit(client.fold((l) => ClientsLoadFailed(_mapFailureToMessage(l)), (r) {
        return ClientsModifiedState(r);
      }));
    } on Exception catch (e) {
      emit(ClientsLoadFailed(e.toString().translateErrorMessage));
    }
  }

  String _mapFailureToMessage(Failure failure) {
    switch (failure.runtimeType) {
      case ServerFailure:
        return SERVER_FAILURE_MESSAGE;
      case EmptyCacheFailure:
        return CACHE_FAILURE_MESSAGE;
      default:
        return 'Unexpected error';
    }
  }
}
