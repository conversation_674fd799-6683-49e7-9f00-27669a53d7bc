part of "../../companies_and_branches.dart";

class CompanyEditDataModel extends CompanyEditDataEntity {
  const CompanyEditDataModel({super.success, super.message, super.data});

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'success': success,
      'message': message,
      'data': (data as EditDataModel).toMap(),
    };
  }

  factory CompanyEditDataModel.fromMap(Map<String, dynamic> map) {
    return CompanyEditDataModel(
      success: map['success'] != null ? map['success'] as bool : null,
      message: map['message'] != null ? map['message'] as String : null,
      data: map['data'] != null
          ? EditDataModel.fromMap(map['data'] as Map<String, dynamic>)
              as EditDataEntity
          : null,
    );
  }

  String toJson() => json.encode(toMap());

  factory CompanyEditDataModel.fromJson(String source) =>
      CompanyEditDataModel.fromMap(json.decode(source) as Map<String, dynamic>);
}

class EditDataModel extends EditDataEntity {
  const EditDataModel({super.company});

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'company': (company as DataModel).toMap(),
    };
  }

  factory EditDataModel.fromMap(Map<String, dynamic> map) {
    return EditDataModel(
      company: map['company'] != null
          ? DataModel.fromMap(map['company'] as Map<String, dynamic>)
          : null,
    );
  }

  String toJson() => json.encode(toMap());

  factory EditDataModel.fromJson(String source) =>
      EditDataModel.fromMap(json.decode(source) as Map<String, dynamic>);
}
