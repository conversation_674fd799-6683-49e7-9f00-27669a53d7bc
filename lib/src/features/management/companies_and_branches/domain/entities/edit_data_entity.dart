part of "../../companies_and_branches.dart";

class CompanyEditDataEntity extends Equatable {
  final bool? success;
  final String? message;
  final EditDataEntity? data;

  const CompanyEditDataEntity({this.success, this.message, this.data});

  @override
  List<Object?> get props => [success, message, data];
}

class EditDataEntity extends Equatable {
  final DataEntity? company;

  const EditDataEntity({this.company});

  @override
  List<Object?> get props => [company];
}
