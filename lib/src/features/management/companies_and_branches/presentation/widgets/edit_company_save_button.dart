import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:optimum_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:optimum_app/src/features/management/companies_and_branches/companies_and_branches.dart';
import 'package:optimum_app/src/features/management/companies_and_branches/presentation/bloc/companies_branches_bloc.dart';

class EditCompanySaveButton extends HookWidget {
  final String id;
  final NewCompanyParams? params;

  const EditCompanySaveButton({
    Key? key,
    required this.params,
    required this.id,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final local = AppLocalizations.of(context)!;

    void onEditCompany() async {
      context.read<CompaniesAndBranchesBloc>().add(EditCompanyEvent(
            id: id,
            params: params!,
          ));
    }

    return BlocBuilder<CompaniesAndBranchesBloc, CompaniesBranchesState>(
        builder: (context, state) {
      final isLoading = state is CompaniesBranchesLoading;
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (isLoading || params == null)
            const LoadingWidget(
              isLinear: true,
            ),
          BaseSaveButton(onPressed: isLoading ? null : () => onEditCompany()),
        ],
      );
    });
  }
}
