part of '../../countries_states.dart';

abstract class CitiesEntity extends Equatable {
  final bool? success;
  final String? message;
  final CitiesDataEntity? data;

  const CitiesEntity({this.success, this.message, this.data});

  @override
  List<Object?> get props => [success, message, data];
}

abstract class CitiesDataEntity extends Equatable {
  final List<CityEntity>? cities;

  const CitiesDataEntity({this.cities});

  @override
  List<Object?> get props => [cities];
}

abstract class CityEntity extends Equatable {
  final int? id;
  final String? nameAr;
  final String? nameEn;
  final String? code;
  final int? stateId;
  final String? createdAt;
  final String? updatedAt;
  final String? name;

  const CityEntity(
      {this.id,
      this.nameAr,
      this.nameEn,
      this.code,
      this.stateId,
      this.createdAt,
      this.updatedAt,
      this.name});

  @override
  List<Object?> get props {
    return [
      id,
      nameAr,
      nameEn,
      code,
      stateId,
      createdAt,
      updatedAt,
      name,
    ];
  }
}
