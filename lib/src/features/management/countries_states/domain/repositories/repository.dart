part of '../../countries_states.dart';

abstract class CountriesAndStatesRepository {
  Future<Either<Failure, CountriesModel>> getCountries();
  Future<Either<Failure, String>> addCountry(NewCountryParams params);
  Future<Either<Failure, String>> editCountry(
      {required NewCountryParams params, required String countryId});
  // Future<Either<Failure, String>> getCountryData(String countryId);
  Future<Either<Failure, String>> deleteCountry(String countryId);
  Future<Either<Failure, StatesModel>> getStates(String countryId);
  Future<Either<Failure, String>> addState(
      {required NewStateParams params, required String countryId});
  Future<Either<Failure, String>> editStates(
      {required NewStateParams params,
      required String countryId,
      required String stateId});
  Future<Either<Failure, String>> getStateData(
      {required String countryId, required String stateId});
  Future<Either<Failure, String>> deleteState(
      {required String countryId, required String stateId});
  Future<Either<Failure, CitiesModel>> getCities(String stateId);
  Future<Either<Failure, String>> addCity(
      {required NewCityParams params,
      required String countryId,
      required String stateId});
  Future<Either<Failure, String>> editCity({
    required NewCityParams params,
    required String countryId,
    required String stateId,
    required String cityId,
  });
  Future<Either<Failure, String>> getCityData(
      {required String countryId,
      required String stateId,
      required String cityId});
  Future<Either<Failure, String>> deleteCity(
      {required String countryId,
      required String stateId,
      required String cityId});
}
