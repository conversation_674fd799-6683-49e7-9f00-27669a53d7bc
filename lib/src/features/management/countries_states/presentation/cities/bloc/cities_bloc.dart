import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:optimum_app/src/core/consts/error/failures.dart';
import 'package:optimum_app/src/core/extensions/extensions.dart';
import 'package:optimum_app/src/features/management/countries_states/countries_states.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'cities_event.dart';
part 'citis_state.dart';

const String SERVER_FAILURE_MESSAGE = 'Server Failure';
const String CACHE_FAILURE_MESSAGE = 'Cache Failure';
const String INVALID_INPUT_FAILURE_MESSAGE =
    'Invalid Input - The number must be a positive integer or zero.';

class CitiesBloc extends Bloc<CityEvent, CitiesState> {
  final SharedPreferences sharedPreferences;
  final GetCitiesUC getCitiesUC;
  final AddCityUC addCitiesUC;
  final EditCityUC editCityUC;
  final DeleteCityUC deleteCityUC;

  CitiesBloc(
      {required this.sharedPreferences,
      required this.getCitiesUC,
      required this.addCitiesUC,
      required this.deleteCityUC,
      required this.editCityUC})
      : super(const CitiesInitial()) {
    on<GetCitiesEvent>(_get);
    on<AddCityEvent>(_add);
    on<EditCityEvent>(_edit);
    on<DeleteCityEvent>(_delete);
    // on<GetCityDataEvent>(_getStatesData);
  }
  FutureOr<void> _get(GetCitiesEvent event, Emitter<CitiesState> emit) async {
    emit(const CitiesLoading());

    try {
      final admin = await getCitiesUC.call(event.stateId);
      emit(admin.fold((l) => CitiesLoadFailed(_mapFailureToMessage(l)), (r) {
        return CitiesLoadSuccess(r);
      }));
    } on Exception catch (e) {
      emit(CitiesLoadFailed(e.toString().translateErrorMessage));
    }
  }

  FutureOr<void> _add(AddCityEvent event, Emitter<CitiesState> emit) async {
    emit(const CitiesLoading());

    final admin =
        await addCitiesUC.call(event.params, event.countryId, event.stateId);
    admin.fold((l) => CitiesLoadFailed(_mapFailureToMessage(l)), (r) {});
  }

  FutureOr<void> _edit(EditCityEvent event, Emitter<CitiesState> emit) async {
    emit(const CitiesLoading());

    final admin = await editCityUC.call(
        event.params, event.countryId, event.stateId, event.cityId);
    admin.fold((l) => CitiesLoadFailed(_mapFailureToMessage(l)), (r) {});
  }

  FutureOr<void> _delete(
      DeleteCityEvent event, Emitter<CitiesState> emit) async {
    emit(const CitiesLoading());

    try {
      final admin = await deleteCityUC.call(
          event.countryId!, event.stateId, event.cityId);
      admin.fold((l) => CitiesLoadFailed(_mapFailureToMessage(l)), (r) {
        return unit;
      });
    } on Exception catch (e) {
      emit(CitiesLoadFailed(e.toString().translateErrorMessage));
    }
  }

  // FutureOr<void> _getStatesData(
  //     GetCityDataEvent event, Emitter<CitiesState> emit) async {
  //   emit(const CitiesLoading());

  //   try {
  //     final admin = await getCityDataUC.call(event.countryId, event.stateId);
  //     admin.fold((l) => CitiesLoadFailed(_mapFailureToMessage(l)), (r) {
  //       return unit;
  //     });
  //   } on Exception catch (e) {
  //     emit(CitiesLoadFailed(e.toString().translateErrorMessage));
  //   }
  // }

  String _mapFailureToMessage(Failure failure) {
    switch (failure.runtimeType) {
      case ServerFailure:
        return SERVER_FAILURE_MESSAGE;
      case EmptyCacheFailure:
        return CACHE_FAILURE_MESSAGE;
      default:
        return 'Unexpected error';
    }
  }
}
