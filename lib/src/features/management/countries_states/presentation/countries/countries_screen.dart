import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:optimum_app/src/core/adaptive_widgets/adaptive_dialog_button.dart';
import 'package:optimum_app/src/core/adaptive_widgets/adaptive_top_bar.dart';
import 'package:optimum_app/src/core/consts/spaces/spaces.dart';
import 'package:optimum_app/src/core/extensions/extensions.dart';
import 'package:optimum_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:optimum_app/src/core/shared_widgets/tables/custom_table.dart';
import 'package:optimum_app/src/core/utils/injector/injector.dart';
import 'package:optimum_app/src/core/utils/nav.dart';
import 'package:optimum_app/src/features/management/countries_states/countries_states.dart';
import 'package:optimum_app/src/features/management/countries_states/presentation/countries/bloc/countries_bloc.dart';
import 'package:optimum_app/src/features/management/countries_states/presentation/states/states_screen.dart';

class CountriesScreen extends HookWidget {
  static const routeName = "/management/countries";

  const CountriesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final txt = AppLocalizations.of(context)!;

    final formKey = GlobalKey<FormState>();
    final nameArCtrl = useTextEditingController();
    final nameEnCtrl = useTextEditingController();
    final codeCtrl = useTextEditingController();
    final nameArSCtrl = useTextEditingController();
    final nameEnSCtrl = useTextEditingController();
    final codeSCtrl = useTextEditingController();

    //  //   Text(txt.code),
    //                   //   Text(txt.nameAr),
    //                   //   Text(txt.nameEn),
    //                   //   Text(txt.countryCode),
    //                   //   Text(txt.actions),
    List<String> titles = [
      txt.code,
      txt.nameAr,
      txt.nameEn,
      txt.countryCode,
      txt.actions,
    ];

    final columnWidget = titles
        .map(
          (e) => CellWidget(e, isTitle: true),
        )
        .toList(growable: false);

    return BlocProvider(
      create: (context) => sl<CountriesBloc>()..add(const GetCountriesEvent()),
      child: BlocConsumer<CountriesBloc, CountriesState>(
        listener: (context, state) {
          if (state is CountriesLoadFailedState) {
            context.showBar(state.message);
          }
          if (state is CountriesModifiedState) {
            context.showBar(state.message);
            context.read<CountriesBloc>().add(const GetCountriesEvent());
          }
        },
        builder: (context, state) {
          return StatefulBuilder(builder: (context, setState) {
            return Scaffold(
                appBar: AdaptiveTopBar(
                  title: txt.countries,
                  actions: [
                    AdaptiveDialogButton(
                      buttonIcon: const Icon(Icons.add),
                      actions: [
                        TextButton(
                            onPressed: () => Navigator.pop(context),
                            child: Text(txt.cancel)),
                        TextButton(
                            onPressed: () {
                              if (!formKey.currentState!.validate()) return;
                              context.read<CountriesBloc>().add(AddCountryEvent(
                                  NewCountryParams(
                                      country: Country(
                                          code: codeCtrl.text,
                                          nameAr: nameArCtrl.text,
                                          nameEn: nameEnCtrl.text))));
                              Navigator.pop(context);
                            },
                            child: Text(txt.ok))
                      ],
                      child: Form(
                        key: formKey,
                        child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                txt.addNewCountry,
                                style: Theme.of(context).textTheme.titleLarge,
                              ),
                              BaseTableTextField(
                                  controller: nameArCtrl,
                                  hintText: txt.nameAr,
                                  onChanged: (value) => setState(() {})),
                              BaseTableTextField(
                                  controller: nameEnCtrl,
                                  hintText: txt.nameEn,
                                  onChanged: (value) => setState(() {})),
                              BaseTableTextField(
                                  controller: codeCtrl,
                                  hintText: txt.code,
                                  onChanged: (value) => setState(() {})),
                            ]),
                      ),
                    )
                  ],
                ),
                body: CustomTable(
                  columns: columnWidget,

                  // [
                  //   Text(txt.code),
                  //   Text(txt.nameAr),
                  //   Text(txt.nameEn),
                  //   Text(txt.countryCode),
                  //   Text(txt.actions),
                  // ],
                  rows: [
                    DataRow(cells: [
                      DataCell.empty,
                      DataCell(BaseTableTextField(
                        controller: nameArSCtrl,
                        hintText: "${txt.searchBy} ${txt.nameAr}",
                      )),
                      DataCell(BaseTableTextField(
                        controller: nameEnSCtrl,
                        hintText: "${txt.searchBy} ${txt.nameEn}",
                      )),
                      DataCell(BaseTableTextField(
                        controller: codeSCtrl,
                        hintText: "${txt.searchBy} ${txt.code}",
                      )),
                      DataCell.empty,
                    ]),
                    if (state is CountriesLoadingState)
                      ...LoadingWidget.loadingTable(5),
                    if (state is CountriesLoadSuccessState)
                      ...state.countries.data!.countries!.where((element) {
                        if (nameArSCtrl.text.isNotEmpty) {
                          return element.nameAr!.contains(nameArSCtrl.text);
                        } else {
                          return true;
                        }
                      }).where((element) {
                        if (nameEnSCtrl.text.isNotEmpty) {
                          return element.nameEn!.contains(nameEnSCtrl.text);
                        } else {
                          return true;
                        }
                      }).where((element) {
                        if (codeSCtrl.text.isNotEmpty) {
                          return element.code!.contains(codeSCtrl.text);
                        } else {
                          return true;
                        }
                      }).map((country) => DataRow(cells: [
                            DataCell(CellWidget(country.id.toString())),
                            DataCell(CellWidget(country.nameAr!)),
                            DataCell(CellWidget(country.nameEn!)),
                            DataCell(CellWidget(country.code!)),
                            DataCell(Wrap(
                              children: [
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: AppSpaces.medium),
                                  child: ElevatedButton(
                                      onPressed: () => NV.nextScreen(
                                          context,
                                          StatesScreen(
                                              countryId:
                                                  country.id.toString())),
                                      child: Text(txt.status)),
                                ),
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: AppSpaces.medium),
                                  child: ElevatedButton(
                                      onPressed: () {}, child: Text(txt.edit)),
                                ),
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: AppSpaces.medium),
                                  child: ElevatedButton(
                                      onPressed: () {},
                                      child: Text(txt.delete)),
                                )
                              ],
                            ))
                          ]))
                  ],
                ));
          });
        },
      ),
    );
  }
}
