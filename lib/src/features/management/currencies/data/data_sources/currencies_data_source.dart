part of "../../currencies.dart";

abstract class CurrencyRemoteDataSource {
  Future<List<CurrencyModel>> getCurrencies();
  Future<List<CurrencyPriceModel>> getCurrencyPrices(int currencyId);
  Future<Unit> addCurrency(CurrencyModel currency);
  Future<Unit> updateCurrency(CurrencyModel currency);
  Future<Unit> deleteCurrency(int id);
}

class CurrencyRemoteDataSourceImpl extends CurrencyRemoteDataSource {
  final _apiService = NetworkApiService();

  @override
  Future<List<CurrencyModel>> getCurrencies() async {
    const endPoint = ApiEndPoints.currency;

    try {
      final response = await _apiService.getResponse(
        endPoint,
      );

      log('CurrencyRemoteDataSourceImplUrl: $endPoint');
      final currency = List<CurrencyModel>.from(
        response['data'].map(
          (x) => CurrencyModel.fromJson(x),
        ),
      );

      return currency;
    } catch (error) {
      log('CurrencyRemoteDataSourceImplError: $error');
      rethrow;
    }
  }

  @override
  Future<List<CurrencyPriceModel>> getCurrencyPrices(int currencyId) async {
    final endPoint = '${ApiEndPoints.currencyPrices}/$currencyId';

    try {
      final response = await _apiService.getResponse(
        endPoint,
      );

      log('CurrencyRemoteDataSourceImplUrl: $endPoint Response: $response');
      final currency = List<CurrencyPriceModel>.from(
        response['data'].map(
          (x) => CurrencyPriceModel.fromJson(x),
        ),
      );

      return currency;
    } catch (error) {
      log('CurrencyRemoteDataSourceImplError: $error');
      rethrow;
    }
  }

  @override
  Future<Unit> addCurrency(CurrencyModel currency) async {
    const endPoint = ApiEndPoints.currency;
    try {
      log('CurrencyRemoteDataSourceImpl: ${currency.toJson()}');
      await _apiService.postResponse(
        endPoint,
        data: currency.toJson(),
      );

      return unit;
    } catch (error) {
      log('CurrencyRemoteDataSourceImplError: $error');
      rethrow;
    }
  }

  @override
  Future<Unit> updateCurrency(CurrencyModel currency) async {
    final endPoint = '${ApiEndPoints.currency}/${currency.id}';

    try {
      await _apiService.putResponse(
        endPoint,
        data: currency.toJson(),
      );

      return unit;
    } catch (error) {
      log('CurrencyRemoteDataSourceImplError: $error');
      rethrow;
    }
  }

  @override
  Future<Unit> deleteCurrency(int id) async {
    final endPoint = '${ApiEndPoints.currency}/$id';

    try {
      await _apiService.deleteResponse(
        endPoint,
      );

      return unit;
    } catch (error) {
      log('CurrencyRemoteDataSourceImplError: $error');
      rethrow;
    }
  }
}
