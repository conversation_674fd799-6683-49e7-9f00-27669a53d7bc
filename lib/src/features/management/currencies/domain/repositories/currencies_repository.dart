import 'package:dartz/dartz.dart';
import 'package:optimum_app/src/core/consts/error/failures.dart';
import 'package:optimum_app/src/features/management/currencies/domain/entities/currencies_price_entity.dart';

import '../entities/currencies_entity.dart';

abstract class CurrenciesRepository {
  Future<Either<Failure, List<CurrencyEntity>>> getCurrencies();
  Future<Either<Failure, List<CurrencyPriceEntity>>> getCurrencyPrices(
      int currencyId);
  Future<Either<Failure, Unit>> addCurrency(CurrencyEntity currencies);
  Future<Either<Failure, Unit>> updateCurrency(CurrencyEntity currencies);
  Future<Either<Failure, Unit>> deleteCurrency(int id);
}
