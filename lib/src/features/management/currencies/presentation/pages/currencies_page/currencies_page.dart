import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:optimum_app/src/core/adaptive_widgets/adaptive_top_bar.dart';
import 'package:optimum_app/src/core/extensions/extensions.dart';
import 'package:optimum_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:optimum_app/src/features/home/<USER>/drawer/app_drawer.dart';
import 'package:optimum_app/src/features/management/currencies/presentation/pages/add_currency/add_currency_page.dart';
import 'package:optimum_app/src/features/management/currencies/presentation/pages/currencies_page/widgets/currencies_table.dart';

import '../../../../../../core/utils/nav.dart';
import '../../cubit/cubit.dart';

class CurrenciesPage extends StatelessWidget {
  static const routeName = '/management/currencies';
  const CurrenciesPage({super.key});

  @override
  Widget build(BuildContext context) {
    final local = AppLocalizations.of(context)!;

    return BlocProvider(
      create: (context) =>
          CurrenciesCubit.getCurrenciesCubitSl()..getCurrencies(),
      child: GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),
        child: BlocListener<CurrenciesCubit, CurrenciesState>(
            listener: (context, state) {
              log('state: ${state.runtimeType}');
              if (state is ErrorCurrenciesState) {
                context.showBar(state.message.translateErrorMessage,
                    isError: true);
              } else if (state is SuccessCurrenciesState) {
                context.showBar(state.message);

                CurrenciesCubit.get(context).getCurrencies();
              }
            },
            child: Scaffold(
                drawer: const AppDrawer(),
                appBar: AdaptiveTopBar(
                  title: local.currencies,
                  actions: [
                    BaseAddCircleButton(
                        onPressed: () => NV.nextScreenNamed(
                            context, AddCurrencyPage.routeName)),
                  ],
                ),
                body: const CurrencyTable())),
      ),
    );
  }
}
