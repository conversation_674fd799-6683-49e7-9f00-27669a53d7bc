import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:optimum_app/src/core/consts/theme/theme.dart';
import 'package:optimum_app/src/core/extensions/extensions.dart';
import 'package:optimum_app/src/features/management/currencies/domain/entities/currencies_entity.dart';

import '../../../../../../../core/consts/spaces/spaces.dart';
import '../../../../domain/entities/currencies_price_entity.dart';

class CurrencyPricesGrid extends StatelessWidget {
  final List<CurrencyPriceEntity> currencyPrices;
  final CurrencyEntity selectedCurrency;
  const CurrencyPricesGrid(
      {Key? key, required this.currencyPrices, required this.selectedCurrency})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final txt = AppLocalizations.of(context)!;

    return Expanded(
      child: GridView(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: AppSpaces.large,
          mainAxisSpacing: AppSpaces.large,
        ),
        children: currencyPrices
            .map((e) => GridTile(
                  child: Card(
                      child: Padding(
                    padding: const EdgeInsets.all(12),
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Text(txt.priceFromCurrency),
                          5.verticalSpace,
                          Text(selectedCurrency.name!,
                              style: context.titleStyle
                                  .copyWith(color: Colors.blueGrey)),
                          const Spacer(),
                          Text(txt.priceToCurrency),
                          const Spacer(),
                          Text(e.currencyName!, style: context.titleStyle),
                          5.verticalSpace,
                          Text(e.price!.toStringAsFixed(2),
                              style: context.labelLargeStyle
                                  .copyWith(color: ColorManager.red)),
                        ],
                      ),
                    ),
                  )),
                ))
            .toList(),
      ),
    );
  }
}
