import '../../domain/entities/representatives_entity.dart';

class RepresentativeModel extends RepresentativeEntity {
  const RepresentativeModel({
    super.id,
    super.nameAr,
    super.nameEn,
    super.status,
    super.percent,
    super.countryId,
    super.stateId,
    super.city,
    super.address,
    super.email,
    super.phone,
    super.phone2,
    super.accountId,
    super.notes,
    super.createdAt,
    super.updatedAt,
    super.deletedAt,
    super.name,
  });

  factory RepresentativeModel.fromJson(Map<String, dynamic> json) {
    return RepresentativeModel(
      id: json['id'],
      nameAr: json['name_ar'] ?? '',
      nameEn: json['name_en'] ?? '',
      status: json['status'] ?? '',
      percent: json['percent'] ?? 0,
      countryId: json['country_id'] ?? 0,
      stateId: json['state_id'] ?? 0,
      city: json['city'] ?? '',
      address: json['address'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'] ?? '',
      phone2: json['phone2'] ?? '',
      accountId: json['account_id'] ?? 0,
      notes: json['notes'] ?? '',
      createdAt: json['created_at'] ?? '',
      updatedAt: json['updated_at'] ?? '',
      deletedAt: json['deleted_at'] ?? '',
      name: json['name'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (id != null) {
      data['id'] = id;
    }
    data['name_ar'] = nameAr;
    data['name_en'] = nameEn;
    data['status'] = status;
    data['percent'] = percent;
    data['country_id'] = countryId;
    data['state_id'] = stateId;
    data['city'] = city;
    data['address'] = address;
    data['email'] = email;
    data['phone'] = phone;
    data['phone2'] = phone2;
    data['account_id'] = accountId;
    data['notes'] = notes;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['deleted_at'] = deletedAt;
    data['name'] = name;
    return data;
  }
}
