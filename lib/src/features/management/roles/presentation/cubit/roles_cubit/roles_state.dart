import 'package:equatable/equatable.dart';

import '../../../../users/domain/entities/user_page_data_entities/roles.dart';

abstract class RolesState extends Equatable {
  const RolesState();

  @override
  List<Object> get props => [];
}

class RolesInitialState extends RolesState {}

//? Get Role States
class LoadingRolesState extends RolesState {}

class LoadedRolesState extends RolesState {
  final List<RolesEntity> roles;

  const LoadedRolesState({required this.roles});

  @override
  List<Object> get props => [roles];
}

//? Get One Role States
class LoadedOneRoleState extends RolesState {
  final RolesEntity role;

  const LoadedOneRoleState({required this.role});

  @override
  List<Object> get props => [role];
}

//? Add & Update & Delete Role States
class LoadingAddUpdateDeleteRolesState extends RolesState {}

class SuccessRolesState extends RolesState {
  final String message;

  const SuccessRolesState({required this.message});

  @override
  List<Object> get props => [message];
}

//? Error Role State
class ErrorRolesState extends RolesState {
  final String message;

  const ErrorRolesState({required this.message});

  @override
  List<Object> get props => [message];
}
