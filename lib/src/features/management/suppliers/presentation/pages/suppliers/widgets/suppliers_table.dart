import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:optimum_app/src/core/shared_entities_and_models/suppliers/main_supplier_entity.dart';
import 'package:optimum_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:optimum_app/src/core/shared_widgets/tables/custom_table.dart';
import 'package:optimum_app/src/core/utils/nav.dart';

import '../../../../../../../core/shared_widgets/dialogs/platform_dialog.dart';
import '../../../cubit/cubit.dart';
import '../../add_supplier/add_supplier_page.dart';

class SuppliersTable extends StatelessWidget {
  const SuppliersTable({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final local = AppLocalizations.of(context)!;

    List<String> titles = [
      local.nameAr,
      local.nameEn,
      local.phone,
      local.email,
      local.actions
    ];

    List<Widget> cells({
      required MainSupplierEntity supplier,
    }) {
      return [
        CellWidget(supplier.nameAr!),
        CellWidget(supplier.nameEn!),
        CellWidget(supplier.phone!),
        CellWidget(supplier.email!),
        ActionButtons(
            onEdit: () => NV.nextScreen(
                context,
                AddSupplierPage(
                  supplier: supplier,
                )),
            onDelete: () => showPlatformDialog(
                  context,
                  isDelete: true,
                  title: local.deleteSupplier,
                  content: local.deleteSupplierMessage,
                  action: () => context
                      .read<SuppliersCubit>()
                      .deleteSupplier(local, id: supplier.id!),
                )),
      ];
    }

    return _Table(
      titles: titles,
      cells: cells,
    );
  }
}

class _Table extends StatelessWidget {
  final List<String> titles;
  final List<Widget> Function({required MainSupplierEntity supplier}) cells;
  const _Table({Key? key, required this.titles, required this.cells})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SuppliersCubit, SuppliersState>(
        builder: (context, suppliersState) {
      if (suppliersState is ErrorSuppliersState) {
        return Center(
          child: Text(suppliersState.message),
        );
      }

      //! Titles Row
      final columnWidget = titles
          .map((e) => CellWidget(e, isTitle: true))
          .toList(growable: false);

      //! Data Rows
      List<DataRow> rowWidget() {
        if (suppliersState is LoadingSuppliersState ||
            suppliersState is LoadingAddUpdateDeleteSupplierState) {
          return LoadingWidget.loadingTable(5);
        } else if (suppliersState is LoadedSuppliersState) {
          return suppliersState.suppliers
              .map((e) => DataRow(
                    cells: cells(supplier: e).map((e) => DataCell(e)).toList(),
                  ))
              .toList(growable: false);
        }
        return [];
      }

      Widget buildTable() {
        void onChangePerPage(value) {
          context.read<SuppliersCubit>().selectedPerPageEntries.value = value;
          context.read<SuppliersCubit>().getAllSuppliers();
        }

        return CustomTable(
          columns: columnWidget,
          selectedPerPageEntriesValue:
              SuppliersCubit.get(context).selectedPerPageEntries,
          onChangePerPage: onChangePerPage,
          showFooter: suppliersState is LoadedSuppliersState &&
              suppliersState.suppliers.isNotEmpty,
          rows: [
            //? Search Fields Row
            DataRow(
              selected: true,
              cells: titles
                  .map((e) => DataCell(
                        _SearchRow(title: e),
                      ))
                  .toList(growable: false),
            ),

            //? Data Rows
            ...rowWidget(),
          ],
        );
      }

      return Stack(
        children: [
          buildTable(),
          if (suppliersState is LoadedSuppliersState &&
              suppliersState.suppliers.isEmpty)
            const Center(
              child: EmptyLottieIcon(),
            )
        ],
      );
    });
  }
}

class _SearchRow extends HookWidget {
  final String title;

  const _SearchRow({Key? key, required this.title}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final local = AppLocalizations.of(context)!;
    final supplierCubit = context.read<SuppliersCubit>();

    if (title != local.actions && title != local.status) {
      late String filterKey;

      if (title == local.nameAr) filterKey = 'name_ar';
      if (title == local.nameEn) filterKey = 'name_en';
      if (title == local.phone) filterKey = 'phone';
      if (title == local.email) filterKey = 'email';

      return BaseTableTextField(
        hintText: title,
        onChanged: (val) {
          supplierCubit.columnFilters.addAll({filterKey: val});
          supplierCubit.getAllSuppliers();
        },
      );
    }

    return const SizedBox();
  }
}
