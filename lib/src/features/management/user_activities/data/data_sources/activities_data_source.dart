import 'dart:developer';

import 'package:optimum_app/src/core/data/remote/network/api_end_points.dart';
import 'package:optimum_app/src/core/data/remote/network/network_api_service.dart';

import '../models/activity_model.dart';

abstract class ActivityRemoteDataSource {
  Future<List<ActivityModel>> getAllActivities(
      {String? columnFilters, int? perPage});
}

class ActivityRemoteDataSourceImpl extends ActivityRemoteDataSource {
  final _apiService = NetworkApiService();

  @override
  Future<List<ActivityModel>> getAllActivities(
      {String? columnFilters, int? perPage}) async {
    final endPoint =
        '${ApiEndPoints.users}?columnFilters=$columnFilters&sortField=id&sortType=Desc&perPage=$perPage&page=1';

    try {
      final response = await _apiService.getResponse(
        endPoint,
      );

      log('ActivityRemoteDataSourceImplUrl: $endPoint');
      final activities = List<ActivityModel>.from(
        response['data']['data'].map(
          (x) => ActivityModel.fromJson(x),
        ),
      );

      return activities;
    } catch (error) {
      log('ActivityRemoteDataSourceImplError: $error');
      rethrow;
    }
  }
}
