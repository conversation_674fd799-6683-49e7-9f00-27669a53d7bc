import '../../../domain/entities/user_page_data_entities/user_page_data_entity.dart';
import 'annuals.dart';
import 'branches_model.dart';
import 'countries.dart';
import 'roles.dart';

class UsersPageDataModel extends UsersPageDataEntity {
  const UsersPageDataModel(
      {super.annuals, super.roles, super.branches, super.countries});

  factory UsersPageDataModel.fromJson(Map<String, dynamic> json) {
    // {
    //   if (json['annuals'] != null) {
    //     annuals = <Annuals>[];
    //     json['annuals'].forEach((v) {
    //       annuals!.add(Annuals.fromJson(v));
    //     });
    //   }
    //   if (json['roles'] != null) {
    //     roles = <Roles>[];
    //     json['roles'].forEach((v) {
    //       roles!.add(Roles.fromJson(v));
    //     });
    //   }
    //   if (json['branches'] != null) {
    //     branches = <Branches>[];
    //     json['branches'].forEach((v) {
    //       branches!.add(Branches.fromJson(v));
    //     });
    //   }
    //   if (json['countries'] != null) {
    //     countries = <Countries>[];
    //     json['countries'].forEach((v) {
    //       countries!.add(Countries.fromJson(v));
    //     });
    //   }
    // }
    return UsersPageDataModel(
      annuals: json['annuals'] != null
          ? (json['annuals'] as List)
              .map((i) => AnnualsModel.fromJson(i))
              .toList()
          : null,
      roles: json['roles'] != null
          ? (json['roles'] as List).map((i) => RolesModel.fromJson(i)).toList()
          : null,
      branches: json['branches'] != null
          ? (json['branches'] as List)
              .map((i) => MainBranchesModel.fromJson(i))
              .toList()
          : null,
      countries: json['countries'] != null
          ? (json['countries'] as List)
              .map((i) => Countries.fromJson(i))
              .toList()
          : null,
    );
  }
}
