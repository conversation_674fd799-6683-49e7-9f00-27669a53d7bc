import 'package:equatable/equatable.dart';

import '../../data/models/notifications_model.dart';

enum NotificationType { info, warning, error }

class MainNotificationsEntity extends Equatable {
  final String? id;
  final NotificationsEntity? notificationData;
  final String? createdAt;
  final String? readAt;

  const MainNotificationsEntity(
      {this.id, this.notificationData, this.createdAt, this.readAt});

  @override
  List<Object?> get props => [id, notificationData, createdAt, readAt];

  static MainNotificationsModel toModel(MainNotificationsEntity entity) {
    return MainNotificationsModel(
        id: entity.id,
        notificationData: entity.notificationData,
        createdAt: entity.createdAt,
        readAt: entity.readAt);
  }
}

class NotificationsEntity extends Equatable {
  final String? subject;
  final String? message;
  final String? url;
  final String? color;
  final bool? withEmail;
  final String? user;
  final NotificationType notificationType;

  const NotificationsEntity(
      {this.subject,
      this.message,
      this.url,
      this.color,
      this.notificationType = NotificationType.info,
      this.withEmail,
      this.user});

  @override
  List<Object?> get props =>
      [subject, message, url, color, withEmail, user, notificationType];
}
