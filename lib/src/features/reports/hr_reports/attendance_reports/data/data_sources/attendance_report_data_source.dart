import 'dart:developer';

import 'package:optimum_app/src/core/data/remote/network/api_end_points.dart';
import 'package:optimum_app/src/core/data/remote/network/base_api_service.dart';
import 'package:optimum_app/src/core/extensions/extensions.dart';
import 'package:optimum_app/src/features/hr/attendance/attendance_details/data/models/attendance_details_model.dart';
import 'package:optimum_app/src/features/reports/hr_reports/attendance_reports/data/models/attendance_report_model.dart';

abstract class AttendanceReportRemoteDataSource {
  Future<List<MainAttendanceReportModel>> getAllAttendanceReports(
      {required String params, required int perPage});
}

class AttendanceReportRemoteDataSourceImpl
    extends AttendanceReportRemoteDataSource {
  final BaseApiService _apiService;

  AttendanceReportRemoteDataSourceImpl(this._apiService);

  @override
  Future<List<MainAttendanceReportModel>> getAllAttendanceReports(
      {required String params, required int perPage}) async {
    final filteredParams = params.filteredParams(params);

    final endPoint =
        // '${HRModuleURLs.attendanceReports}?branch_id=1&employees[]=10&employees[]=15&employees[]=17&employees[]=16&department=2&section=4&start=2023-1-1&end=2023-10-4&still_working=1&reportTitle=%D8%A7%D9%84%D8%AD%D8%B6%D9%88%D8%B1+%D9%88%D8%A7%D9%84%D8%A7%D9%86%D8%B5%D8%B1%D8%A7%D9%81&reportSchema=Daily+attendance+record&reportType=Total&company_name=%D8%B9%D9%84%D8%AC%D9%88%D9%86&branch_name=%D8%A7%D9%84%D8%A8%D8%A7%D8%AD%D8%A9+-+postman&reportHeader=%7B%22Company%22:%22%D8%B9%D9%84%D8%AC%D9%88%D9%86%22,%22Branch%22:%22%D8%A7%D9%84%D8%A8%D8%A7%D8%AD%D8%A9+-+postman%22,%22Date%22:%22%D9%85%D9%86:+2023-1-1+%D8%A7%D9%84%D9%89:+2023-10-4%22%7D';
    // http://dev.opti4it.com/api/v1/hr/reports/attendance?branch=1department=2&section=4start=2023-09-01&end=2023-10-31employees[]=10still_working=true&report_type=TotalreportTitle=تقارير الحضور والانصراف&reportType=Total&company_name=3algoon Office&branch_name=الباحة - postman&lang=en&reportSchema=Daily+attendance+record&per_page=10&page=1


        '${HRModuleURLs.attendanceReports}?$filteredParams&reportSchema=Daily+attendance+record&per_page=$perPage&page=1';
    // '${HRModuleURLs.attendanceReports}?$filteredParams&&reportSchema=Daily+attendance+record&per_page=$perPage&page=1';
    try {
      final response = await _apiService.getResponse(
        endPoint,
      );

      log('Dataaaa $response');

      if (response['data'] == null) {
        return [];
      }

      final map = response['data'] as Map<String, dynamic>;

      final keys = map.keys.toList();

      // "2023-09-01": {
      //             "werrrrrrrrqq": { ....}
      //convert to main attendance report model

      final reportsDate = keys.map((key) {
        log('asfasfsfa ${map[key]} fffff ${map[key].keys.toList()[0]}');
        final employeeName = map[key].keys.toList()[0];
        final attendanceDetailsReports = map[key][employeeName];

        return MainAttendanceReportModel.fromJson(
            date: key,
            attendanceDetailsReports: (
              employeeName,
              attendanceDetailsReports != null
                  ? AttendanceDetailsModel.fromJson(attendanceDetailsReports)
                  : null,

              // List<AttendanceDetailsModel>.from(attendanceDetailsReports
              //         .map((x) => AttendanceDetailsModel.fromJson(x)))
            ));
      }).toList();

      // final dailyAchievementsReports = keys
      //     .map((key) => MainAttendanceReportModel.fromJson(
      //           date: key,
      //           reports: response['data']['achievements'][key],
      //         ))
      //     .toList();

      // final reportsDate = List<MainAttendanceReportModel>.from(
      //     response['data'].map((x) => MainAttendanceReportModel.fromJson(x)));

      return reportsDate;
    } catch (error) {
      log('AttendanceReportRemoteDataSourceImplError: $error');
      rethrow;
    }
  }
}
