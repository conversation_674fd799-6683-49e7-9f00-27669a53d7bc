import 'dart:core';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:optimum_app/src/core/adaptive_widgets/adaptive_top_bar.dart';
import 'package:optimum_app/src/core/consts/strings/consts.dart';
import 'package:optimum_app/src/features/home/<USER>/drawer/app_drawer.dart';
import 'package:optimum_app/src/features/hr/employees/employees_list/employees_imports.dart';
import 'package:optimum_app/src/features/hr/main_details/departments_and_sections/domain/entities/department_entity.dart';
import 'package:optimum_app/src/features/hr/main_details/departments_and_sections/presentation/cubit/cubit.dart';
import 'package:optimum_app/src/features/settings/general_settings/domain/entities/companies_branches_entity.dart';
import 'package:optimum_app/src/features/settings/general_settings/peresentation/cubit/general_settings_cubit.dart';

import '../cubit/bonuses_reports_cubit.dart';
import '../widgets/bonuses_report_bottom_bar.dart';
import '../widgets/bonuses_report_fields.dart';
import '../widgets/bonuses_reports_table.dart';

class BonusReportsPage extends HookWidget {
  static const routeName = '/reports/hr/bonuses';

  const BonusReportsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final local = AppLocalizations.of(context)!;

    Map<String, ValueNotifier> valueNotifiers = {
      Consts.selectedCompany: useState<CompanyEntity?>(null),
      Consts.selectedBranch: useState<BranchEntity?>(null),
      Consts.selectedDepartment: useState<DepartmentEntity?>(null),
      Consts.selectedSection: useState<DepartmentEntity?>(null),
      Consts.selectedEmployee: useState<List<EmployeeEntity>?>(null),
      Consts.stillWorking: useState<bool?>(true),
    };

    final showResultReport = useState(false);

    bool isResultShown = showResultReport.value;

    return MultiBlocProvider(
      providers: [
        // BlocProvider(
        //   create: (context) => TransactionsCubit.getTransactionsCubitSl()
        //     ..getTransactionsPageData(),
        // ),
        BlocProvider(
            create: (context) =>
                GeneralSettingsCubit.getGeneralSettingsCubitSl()
                  ..getAllGeneralSettings()),
        BlocProvider(
            create: (context) => DepartmentsCubit.getDepartmentsCubitSl()
              ..getDepartmentsAndSections()),
        BlocProvider(create: (context) => EmployeesCubit.getEmployeesCubitSl()),
        BlocProvider(
            create: (context) => BonusReportsCubit.getBonusReportsCubitSl()),
      ],
      child: Scaffold(
        drawer: const AppDrawer(),
        appBar: AdaptiveTopBar(
          title: local.promotionsAndBonuses,
        ),
        bottomNavigationBar: BonusReportBottomBar(
          showResultReport: showResultReport,
          valueNotifiers: valueNotifiers,
        ),
        body: AnimatedSwitcher(
            duration: const Duration(milliseconds: 300),
            switchInCurve: Curves.easeIn,
            switchOutCurve: Curves.easeOut,
            child: isResultShown
                ? BonusReportTable(
                    valueNotifiers: valueNotifiers,
                  )
                : BonusReportsFields(valueNotifiers: valueNotifiers)),
      ),
    );
  }
}
