import 'dart:io';

import 'package:dartz/dartz.dart';
import 'package:optimum_app/src/core/consts/error/exceptions.dart';
import 'package:optimum_app/src/core/consts/error/failures.dart';
import 'package:optimum_app/src/features/reports/hr_reports/leave_work_report/domain/entities/leave_work_report_entity.dart';

import '../../domain/repositories/leave_work_report_repository.dart';
import '../data_sources/leave_work_report_data_source.dart';

class LeaveWorkReportsRepositoryImpl implements LeaveWorkReportsRepository {
  final LeaveWorkReportRemoteDataSource remoteDataSource;

  LeaveWorkReportsRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, List<LeaveWorkReportEntity>>> getLeaveWorkReports(
      {required String params, required int perPage}) async {
    try {
      final remoteLeaveWorkReports =
          await remoteDataSource.getAllLeaveWorkReports(
        params: params,
        perPage: perPage,
      );
      return Right(remoteLeaveWorkReports);
    } on ServerException {
      return Left(ServerFailure());
    } on SocketException {
      return Left(OfflineFailure());
    }
  }
}
