import 'dart:convert';

import 'package:dartz/dartz.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:optimum_app/src/core/consts/strings/consts.dart';
import 'package:optimum_app/src/core/extensions/extensions.dart';
import 'package:optimum_app/src/features/hr/employees/employees_list/employees_imports.dart';
import 'package:optimum_app/src/features/hr/main_details/departments_and_sections/domain/entities/department_entity.dart';
import 'package:optimum_app/src/features/reports/hr_reports/leave_work_report/domain/entities/leave_work_report_entity.dart';
import 'package:optimum_app/src/features/settings/general_settings/domain/entities/companies_branches_entity.dart';

import '../../../../../../core/consts/error/failures.dart';
import '../../../../../../core/utils/injector/injector.dart';
import '../../domain/use_cases/get_leave_work_report.dart';
import 'cubit.dart';

class LeaveWorkReportsCubit extends Cubit<LeaveWorkReportsState> {
  final GetLeaveWorkReportsUseCase getAllLeaveWorkReportsUseCase;

  LeaveWorkReportsCubit({
    required this.getAllLeaveWorkReportsUseCase,
  }) : super(LeaveWorkReportsInitialState());

  static LeaveWorkReportsCubit get(context) => BlocProvider.of(context);

  static LeaveWorkReportsCubit getLeaveWorkReportsCubitSl() =>
      sl<LeaveWorkReportsCubit>();

  String params = '';

  final ValueNotifier<int> selectedPerPageEntries = ValueNotifier(10);

  void getAllLeaveWorkReports() async {
    try {
      emit(LoadingLeaveWorkReportsState());

      final result = await getAllLeaveWorkReportsUseCase.call(
        params: jsonEncode(params),
        perPage: selectedPerPageEntries.value,
      );

      emit(_eitherGetLeaveWorkReportOrErrorState(result));
    } catch (e) {
      emit(ErrorLeaveWorkReportsState(
          message: e.toString().translateErrorMessage));
    }
  }

  static GlobalKey<FormState> formKey = GlobalKey<FormState>();

  LeaveWorkReportsState _eitherGetLeaveWorkReportOrErrorState(
    Either<Failure, List<LeaveWorkReportEntity>> result,
  ) {
    return result.fold(
      (failure) => ErrorLeaveWorkReportsState(
          message: failure.toString().translateErrorMessage),
      (invoiceReports) =>
          LoadedLeaveWorkReportsState(invoiceReports: invoiceReports),
    );
  }

  //? Set Report Notifiers To Params ------------------------------------------
  void setFieldsToParams(
    BuildContext context, {
    required Map<String, ValueNotifier> valueNotifiers,
  }) {
    params = '';
    _selectedBranchFilters(valueNotifiers: valueNotifiers);
    _selectedDepartmentAndSectionFilters(valueNotifiers: valueNotifiers);
    _selectedEmployeeFilters(valueNotifiers: valueNotifiers);
    _fromAndToDateFilters(valueNotifiers: valueNotifiers);
    _otherFilters(valueNotifiers: valueNotifiers);
    _reportTitleAndHeaderFilters(context, valueNotifiers: valueNotifiers);
  }

  //? Branch Filters -----------------------------------------------------
  void _selectedBranchFilters({
    required Map<String, ValueNotifier> valueNotifiers,
  }) {
    final selectedBranch =
        valueNotifiers[Consts.selectedBranch] as ValueNotifier<BranchEntity?>;

    final companyAndBranchFilter = 'branch=${selectedBranch.value?.id}';

    params += companyAndBranchFilter;
  }

  //? Department & Section Filters --------------------------------------------
  void _selectedDepartmentAndSectionFilters({
    required Map<String, ValueNotifier> valueNotifiers,
  }) {
    final selectedDepartment = valueNotifiers[Consts.selectedDepartment]
        as ValueNotifier<DepartmentEntity?>;
    final selectedSection = valueNotifiers[Consts.selectedSection]
        as ValueNotifier<DepartmentEntity?>;

    final departmentAndSectionFilter =
        'department=${selectedDepartment.value?.id}&section=${selectedSection.value?.id}';

    params += departmentAndSectionFilter;
  }

  //? From & To Date Filters --------------------------------------------------
  void _fromAndToDateFilters({
    required Map<String, ValueNotifier> valueNotifiers,
  }) {
    final startDate =
        valueNotifiers[Consts.startDate] as ValueNotifier<DateTime?>;
    final endDate = valueNotifiers[Consts.endDate] as ValueNotifier<DateTime?>;

    final fromAndToDateFilter =
        '&from_date=${startDate.value?.toIso8601String()}&to_date=${endDate.value?.toIso8601String()}';

    params += fromAndToDateFilter;
  }

  //? Employee Filters --------------------------------------------------------
  void _selectedEmployeeFilters({
    required Map<String, ValueNotifier> valueNotifiers,
  }) {
    final selectedEmployee = valueNotifiers[Consts.selectedEmployee]
        as ValueNotifier<List<EmployeeEntity>?>;

    String employeeFilter = '';

    if (selectedEmployee.value != null) {
      for (int i = 0; i < selectedEmployee.value!.length; i++) {
        employeeFilter += '&employees[]=${selectedEmployee.value![i].id}';
      }
    }

    params += employeeFilter;
  }

  //? Other Filters (still_working,Statement, Report Type)--------------------------------------------------------
  void _otherFilters({
    required Map<String, ValueNotifier> valueNotifiers,
  }) {
    final stillWorking =
        valueNotifiers[Consts.stillWorking] as ValueNotifier<bool?>;
    final selectedStatement = valueNotifiers[Consts.selectedStatement]
        as ValueNotifier<List<String?>>;
    final reportType =
        valueNotifiers[Consts.reportType] as ValueNotifier<String>;

    String statementFilter = '';

    for (int i = 0; i < selectedStatement.value.length; i++) {
      statementFilter += 'statement[]=${selectedStatement.value[i]}&';
    }

    final otherFilters =
        'still_working=${stillWorking.value}&${statementFilter}report_type=${reportType.value}';

    params += otherFilters;
  }

  //? Report Title & Header --------------------------------------------------
  void _reportTitleAndHeaderFilters(
    BuildContext context, {
    required Map<String, ValueNotifier> valueNotifiers,
  }) {
    final selectedCompany =
        valueNotifiers[Consts.selectedCompany] as ValueNotifier<CompanyEntity?>;
    final selectedBranch =
        valueNotifiers[Consts.selectedBranch] as ValueNotifier<BranchEntity?>;

    //? Report Title & Header --------------------------------------------------
    final reportTitle =
        'reportTitle=تقارير البيانات الأساسية&reportType=Total&company_name=${selectedCompany.value?.name}&branch_name=${selectedBranch.value?.name}';

    params += '$reportTitle&lang=${context.languageCode}';
  }
}
