import 'package:dartz/dartz.dart';

import '../../../../../../core/consts/error/failures.dart';
import '../../../../../hr/employees/employees_list/employees_imports.dart';
import '../repositories/main_details_report_repository.dart';

class GetHrMainDetailsReportsUseCase {
  final HrMainDetailsReportsRepository repository;

  GetHrMainDetailsReportsUseCase(this.repository);

  Future<Either<Failure, List<EmployeeEntity>>> call(
      {required String params, required int perPage}) async {
    return await repository.getHrMainDetailsReports(
        params: params, perPage: perPage);
  }
}
