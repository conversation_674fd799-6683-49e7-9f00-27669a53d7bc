import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:optimum_app/src/core/extensions/extensions.dart';
import 'package:optimum_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:optimum_app/src/core/shared_widgets/tables/custom_table.dart';
import 'package:optimum_app/src/features/hr/employees/employees_list/employees_imports.dart';

import '../cubit/cubit.dart';

class HrMainDetailsReportTable extends HookWidget {
  final Map<String, ValueNotifier> valueNotifiers;

  const HrMainDetailsReportTable({
    Key? key,
    required this.valueNotifiers,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final local = AppLocalizations.of(context)!;

    List<String> titles = [
      local.code,
      local.name,
      local.section,
      local.job,
    ];

    List<Widget> cells({
      required EmployeeEntity invoiceReport,
    }) {
      return [
        CellWidget(invoiceReport.id?.toString() ?? ''),
        CellWidget(invoiceReport.name ?? ''),
        CellWidget(invoiceReport.section?.name ?? ''),
        CellWidget(invoiceReport.job?.name ?? ''),
      ];
    }

    return _Table(
      titles: titles,
      cells: cells,
    );
  }
}

class _Table extends StatelessWidget {
  final List<String> titles;
  final List<Widget> Function({required EmployeeEntity invoiceReport}) cells;

  const _Table({Key? key, required this.titles, required this.cells})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HrMainDetailsReportsCubit, HrMainDetailsReportsState>(
        builder: (context, invoiceReportsState) {
      if (invoiceReportsState is ErrorHrMainDetailsReportsState) {
        return Padding(
          padding: const EdgeInsets.all(8.0),
          child: Center(
            child: Text(invoiceReportsState.message.translateErrorMessage),
          ),
        );
      }

      //! Titles Row
      final columnWidget = titles
          .map((e) => CellWidget(e, isTitle: true))
          .toList(growable: false);

      //! Data Rows
      List<DataRow> rowWidget() {
        if (invoiceReportsState is LoadingHrMainDetailsReportsState) {
          return LoadingWidget.loadingTable(titles.length);
        } else if (invoiceReportsState is LoadedHrMainDetailsReportsState) {
          return invoiceReportsState.invoiceReports
              .map((e) => DataRow(
                    cells: cells(invoiceReport: e)
                        .map((e) => DataCell(e))
                        .toList(),
                  ))
              .toList(growable: false);
        }
        return [];
      }

      Widget buildTable() {
        void onChangePerPage(value) {
          context
              .read<HrMainDetailsReportsCubit>()
              .selectedPerPageEntries
              .value = value;
          context
              .read<HrMainDetailsReportsCubit>()
              .getAllHrMainDetailsReports();
        }

        return CustomTable(
          columns: columnWidget,
          selectedPerPageEntriesValue:
              HrMainDetailsReportsCubit.get(context).selectedPerPageEntries,
          onChangePerPage: onChangePerPage,
          showFooter: invoiceReportsState is LoadedHrMainDetailsReportsState &&
              invoiceReportsState.invoiceReports.isNotEmpty,
          rows: rowWidget(),
        );
      }

      return Stack(
        children: [
          buildTable(),
          if (invoiceReportsState is LoadedHrMainDetailsReportsState &&
              invoiceReportsState.invoiceReports.isEmpty)
            const Center(
              child: EmptyLottieIcon(),
            )
        ],
      );
    });
  }
}
