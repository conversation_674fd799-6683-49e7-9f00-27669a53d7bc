part of '../../report_settings.dart';

abstract class ReportSettingsState extends Equatable {
  const ReportSettingsState();

  @override
  List<Object?> get props => [];
}

class ReportSettingsInitialState extends ReportSettingsState {}

//? Get ReportSetting States
class LoadingReportSettingsState extends ReportSettingsState {}

class LoadedReportSettingsState extends ReportSettingsState {
  final dynamic printedReport;

  const LoadedReportSettingsState({required this.printedReport});

  @override
  List<Object?> get props => [printedReport];
}

//? Error ReportSetting State
class ErrorReportSettingsState extends ReportSettingsState {
  final String message;

  const ErrorReportSettingsState({required this.message});

  @override
  List<Object> get props => [message];
}
