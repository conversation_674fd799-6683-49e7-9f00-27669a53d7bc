import 'package:optimum_app/src/features/settings/general_settings/domain/entities/companies_branches_entity.dart';

class CompanyModel extends CompanyEntity {
  const CompanyModel(
      {super.id, super.nameEn, super.nameAr, super.name, super.branches});

  factory CompanyModel.fromJson(Map<String, dynamic> json) {
    return CompanyModel(
      id: json['id'],
      nameEn: json['name_en'],
      nameAr: json['name_ar'],
      name: json['name'],
      branches: json['branches'] != null
          ? (json['branches'] as List)
              .map((i) => BranchModel.fromJson(i))
              .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name_en'] = nameEn;
    data['name_ar'] = nameAr;
    data['name'] = name;
    if (branches != null) {
      data['branches'] =
          branches!.map((v) => BranchEntity.toModel(v).toJson()).toList();
    }
    return data;
  }

  @override
  List<Object?> get props => [id, nameEn, nameAr, name, branches];
}

class BranchModel extends BranchEntity {
  const BranchModel(
      {super.id,
      super.name,
      super.companyId,
      super.currancyId,
      super.currancy,
      super.accountingSetting});

  factory BranchModel.fromJson(Map<String, dynamic> json) {
    return BranchModel(
      id: json['id'],
      name: json['name'],
      companyId: json['company_id'],
      currancyId: json['currancy_id'],
      currancy: json['currancy'] != null
          ? CurrancyModel.fromJson(json['currancy'])
          : null,
      accountingSetting: json['accounting_setting'] != null
          ? AccountingSettingModel.fromJson(json['accounting_setting'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['company_id'] = companyId;
    data['currancy_id'] = currancyId;
    // if (currancy != null) {
    //   data['currancy'] = currancy!.toJson();
    // }
    // if (accountingSetting != null) {
    //   data['accounting_setting'] = accountingSetting!.toJson();
    // }//TODO-Check
    return data;
  }

  @override
  List<Object?> get props =>
      [id, name, companyId, currancyId, currancy, accountingSetting];
}

class CurrancyModel extends AllCurrancyEntity {
  const CurrancyModel(
      {super.id,
      super.name,
      super.symbol,
      super.price,
      super.createdAt,
      super.updatedAt,
      super.currancyExchanges});

  factory CurrancyModel.fromJson(Map<String, dynamic> json) {
    return CurrancyModel(
      id: json['id'],
      name: json['name'],
      symbol: json['symbol'],
      price: json['price'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
      currancyExchanges: json['currancy_exchanges'] != null
          ? (json['currancy_exchanges'] as List)
              .map((i) => CurrancyExchangesModel.fromJson(i))
              .toList()
          : null,
    );
  }

  @override
  List<Object?> get props =>
      [id, name, symbol, price, createdAt, updatedAt, currancyExchanges];
}

class CurrancyExchangesModel extends CurrancyExchangesEntity {
  const CurrancyExchangesModel(
      {super.id, super.name, super.symbol, super.price, super.currancyFromId});

  factory CurrancyExchangesModel.fromJson(Map<String, dynamic> json) {
    return CurrancyExchangesModel(
      id: json['id'],
      name: json['name'],
      symbol: json['symbol'],
      price: json['price'],
      currancyFromId: json['currancy_from_id'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['symbol'] = symbol;
    data['price'] = price;
    data['currancy_from_id'] = currancyFromId;
    return data;
  }

  @override
  List<Object?> get props => [id, name, symbol, price, currancyFromId];
}

class AccountingSettingModel extends AccountingSettingEntity {
  const AccountingSettingModel(
      {super.id,
      super.profitLossAccountId,
      super.currancyDifferanceAccountId,
      super.representativeCommissionAccountId,
      super.representativeAccountId,
      super.clientAccountId,
      super.importerAccountId,
      super.bankAccountId,
      super.chequeDebitAccountId,
      super.chequeCreditAccountId,
      super.voucherDebitAccountId,
      super.voucherDebitSerialType,
      super.allowChangeVoucherDebit,
      super.voucherDebitSerialStart,
      super.voucherCreditAccountId,
      super.voucherCreditSerialType,
      super.allowChangeVoucherCredit,
      super.voucherCreditSerialStart,
      super.hideDateInVouchers,
      super.disablePrinting,
      super.branchId,
      super.createdAt,
      super.updatedAt});

  factory AccountingSettingModel.fromJson(Map<String, dynamic> json) {
    return AccountingSettingModel(
      id: json['id'],
      profitLossAccountId: json['profit_loss_account_id'],
      currancyDifferanceAccountId: json['currancy_differance_account_id'],
      representativeCommissionAccountId:
          json['representative_commission_account_id'],
      representativeAccountId: json['representative_account_id'],
      clientAccountId: json['client_account_id'],
      importerAccountId: json['importer_account_id'],
      bankAccountId: json['bank_account_id'],
      chequeDebitAccountId: json['cheque_debit_account_id'],
      chequeCreditAccountId: json['cheque_credit_account_id'],
      voucherDebitAccountId: json['voucher_debit_account_id'],
      voucherDebitSerialType: json['voucher_debit_serial_type'],
      allowChangeVoucherDebit: json['allow_change_voucher_debit'],
      voucherDebitSerialStart: json['voucher_debit_serial_start'],
      voucherCreditAccountId: json['voucher_credit_account_id'],
      voucherCreditSerialType: json['voucher_credit_serial_type'],
      allowChangeVoucherCredit: json['allow_change_voucher_credit'],
      voucherCreditSerialStart: json['voucher_credit_serial_start'],
      hideDateInVouchers: json['hide_date_in_vouchers'],
      disablePrinting: json['disable_printing'],
      branchId: json['branch_id'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
    );
  }

  @override
  List<Object?> get props => [
        id,
        profitLossAccountId,
        currancyDifferanceAccountId,
        representativeCommissionAccountId,
        representativeAccountId,
        clientAccountId,
        importerAccountId,
        bankAccountId,
        chequeDebitAccountId,
        chequeCreditAccountId,
        voucherDebitAccountId,
        voucherDebitSerialType,
        allowChangeVoucherDebit,
        voucherDebitSerialStart,
        voucherCreditAccountId,
        voucherCreditSerialType,
        allowChangeVoucherCredit,
        voucherCreditSerialStart,
        hideDateInVouchers,
        disablePrinting,
        branchId,
        createdAt,
        updatedAt
      ];
}
