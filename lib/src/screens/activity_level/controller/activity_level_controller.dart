import 'package:connectify_app/src/screens/activity_level/repo/activity_level_repo.dart';
import 'package:connectify_app/src/screens/notification/model/notification_model.dart';
import 'package:connectify_app/src/screens/notification/repo/notification_repo.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../model/activity_level_model.dart';

final activityLevelControllerProvider =
    Provider.family<ActivityLevelController, BuildContext>((ref, context) {
  final activityLevelRepo = ref.watch(activityLevelRepoProvider);

  return ActivityLevelController(activityLevelRepo: activityLevelRepo, context: context);
});

final activityLevelChangeNotifierControllerProvider =
    ChangeNotifierProvider.family<ActivityLevelController, BuildContext>((ref, context) {
  final activityLevelRepo = ref.watch(activityLevelRepoProvider);

  return ActivityLevelController(activityLevelRepo: activityLevelRepo, context: context);
});

final getAllActivityLevelData =
    FutureProvider.family<List<ActivityLevelModel>, BuildContext>((ref, context) {
  final activityLevelRepo = ref.watch(activityLevelRepoProvider);

  final activityLevelController = ActivityLevelController(activityLevelRepo: activityLevelRepo, context: context);

  return activityLevelController.getActivityLevelData();
});

class ActivityLevelController extends BaseVM {
  final ActivityLevelRepo activityLevelRepo;
  final BuildContext context;

  ActivityLevelController({required this.activityLevelRepo, required this.context});

  Future<List<ActivityLevelModel>> getActivityLevelData() async {
    return await baseFunction(context, () async {
      final activityLevelData = await activityLevelRepo.getActivityLevelData();

      return activityLevelData;
    });
  }

  //? Add Activity Level ========================================================
  Future<void> addActivityLevel(
      {required int activityLevelValue,
      required String? note,
      required final StudentModel? student}) async {
    return await baseFunction(context, () async {
      final getActivityLevelCurrentVal =
          ActivityLevelModel.getActivityLevelValue(value: activityLevelValue);

      final activityLevel = ActivityLevelModel(
          activityLevel: getActivityLevelCurrentVal,
          note: note,
          student: student);

      await activityLevelRepo.addActivityLevel(activityLevel: activityLevel);

      NotificationService.sendNotification(
        title: "Activity Level Update",
        body:
            "Activity level (${activityLevel.activityLevel?.name}) has been added to ${student?.name}'s record",
        userTokenOrTopic: NurseryModelHelper.parentByStudentTopic(student?.id),
        isTopic: true,
      );

      postNewNotification(
          notificationModel: NotificationModel(
        title: "Activity Level Update",
        body:
            "Activity level (${activityLevel.activityLevel?.name}) has been added to ${student?.name}'s record",
        topic: NurseryModelHelper.parentByStudentTopic(student?.id),
      ));
    });
  }
}
