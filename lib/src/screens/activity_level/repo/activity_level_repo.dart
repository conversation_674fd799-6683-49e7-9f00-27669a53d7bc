import 'package:connectify_app/src/screens/activity_level/model/activity_level_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../history/model/history_model.dart';
import '../../history/repo/history_repo.dart';

// * =========================================================

final activityLevelRepoProvider = Provider((ref) {
  final networkApiServices = ref.watch(networkServiceProvider);

  return ActivityLevelRepo(networkApiServices);
});

//? ========================================================

class ActivityLevelRepo with BaseRepository {
  final BaseApiServices _networkApiServices;

  ActivityLevelRepo(this._networkApiServices);

//? get Activity Level Data ========================================================
  Future<List<ActivityLevelModel>> getActivityLevelData() async {
    return await baseFunction(() async {
      final response = _networkApiServices.getResponse(ApiEndpoints.activityLevel);

      final activityLevelData = await compute(responseToActivityLevelModelList, response);

      return activityLevelData;
    });
  }

//? Add Activity Level ========================================================

  Future<void> addActivityLevel({required ActivityLevelModel activityLevel}) async {
    return await baseFunction(() async {
      final activityLevelData = await _networkApiServices
          .postResponse(ApiEndpoints.editDeleteActivityLevel, body: activityLevel.toJson());

      addNewHistory(
          historyModel: HistoryModel(
        historyType: HistoryType.activityLevel,
        activityLevel: ActivityLevelModel(
          id: activityLevelData['data']['id'],
        ),
        student: activityLevel.student,
      ));
    });
  }
}
