import 'dart:developer';

import 'package:connectify_app/src/screens/activity_level/controller/activity_level_controller.dart';
import 'package:connectify_app/src/screens/activity_level/view/widgets/add_activity_level_widgets.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../shared/widgets/shared_widgets.dart';

Future<void> showAddActivityLevelDialog(context) {
  return showDialog(
    context: context,
    builder: (context) {
      return HookConsumer(
        builder: (context, ref, child) {
          final activityLevelChangeNotifierCtrl =
              ref.watch(activityLevelChangeNotifierControllerProvider(context));

          final valueNotifiers = {
            ApiStrings.students: useState<List<StudentModel>>([]),
            ApiStrings.activityLevel: useState<int>(0),
            ApiStrings.note: useState<String>(''),
          };

          //!-----------------------------------------------------

          final formKey = useState(GlobalKey<FormState>());

          //!-----------------------------------------------------

          Future<void> addActivityLevel() async {
            if (!formKey.value.currentState!.validate()) return;
            final students = valueNotifiers[ApiStrings.students]!.value
                as List<StudentModel>;
            log('Activity Level Students: $students');
            await Future.forEach(
              students,
              (student) async {
                await activityLevelChangeNotifierCtrl.addActivityLevel(
                  activityLevelValue:
                      valueNotifiers[ApiStrings.activityLevel]?.value as int,
                  note: valueNotifiers[ApiStrings.note]?.value as String?,
                  student: student,
                );
              },
            );

            if (!context.mounted) return;
            context.back();
            context.showBarMessage(context.tr.addedSuccessfully);
          }

          //!-----------------------------------------------------

          return AlertDialogWidget(
              header: context.tr.addActivityLevel,
              isLoading: activityLevelChangeNotifierCtrl.isLoading,
              isImage: false,
              child: Form(
                key: formKey.value,
                child: AddActivityLevelWidgets(
                  valueNotifiers: valueNotifiers,
                ),
              ),
              onConfirm: () async => await addActivityLevel());
        },
      );
    },
  );
}
