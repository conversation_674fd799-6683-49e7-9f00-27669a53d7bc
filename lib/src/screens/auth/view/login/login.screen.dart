import 'package:barber_app/generated/assets.gen.dart';
import 'package:barber_app/src/core/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import 'widgets/login_fields.widget.dart';

//? filter by city for providers
//? loop closest providers in city by map
//? reserve now in provider details
//? reserve later in service page
//? available and no orders in reserve order

class LoginScreen extends HookConsumerWidget {
  const LoginScreen({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final formKey = useMemoized(() => GlobalKey<FormBuilderState>());

    return Scaffold(
      body: FormBuilder(
        key: form<PERSON><PERSON>,
        child: <PERSON><PERSON><PERSON>(
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(
              horizontal: AppSpaces.padding12,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AppGaps.gap24,
                ClipRRect(
                  borderRadius: BorderRadius.circular(AppRadius.radius28),
                  child: Assets.images.logo.image(
                    fit: BoxFit.cover,
                    width: 200.w,
                  ),
                ).center(),

                AppGaps.gap48,

                Text(
                  context.tr.login,
                  style: AppTextStyles.subHeadLine.copyWith(
                    fontWeight: FontWeight.normal,
                  ),
                ).center(),

                AppGaps.gap24,

                // * Fields Container
                LoginFieldsWidget(
                  formKey: formKey,
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
