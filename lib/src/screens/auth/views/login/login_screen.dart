library auth;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:opti4t_tasks/src/core/consts/app_constants.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/core/shared_widgets/animated/lottie_icon.dart';
import 'package:opti4t_tasks/src/core/shared_widgets/shared_widgets.dart';
import 'package:opti4t_tasks/src/core/theme/color_manager.dart';
import 'package:opti4t_tasks/src/screens/auth/controllers/auth_controller.dart';
import 'package:opti4t_tasks/src/screens/auth/views/register/register_screen.dart';
import 'package:opti4t_tasks/src/screens/home/<USER>/home_screen.dart';
import 'package:xr_helper/xr_helper.dart';

part 'widgets/login_button.dart';
part 'widgets/login_fields.dart';

class LoginScreen extends HookWidget {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final emailController = useTextEditingController(
      text: kDebugMode ? AppConsts.testEmail : '',
    );

    final passwordController = useTextEditingController(
      text: kDebugMode ? AppConsts.testPass : '',
    );

    useEffect(() {
      NotificationService.listenToNotifications(
        onMessage: (message) {
          final notification = message.notification;

          final notificationMessage =
              '${notification?.title ?? ''}\n${notification?.body ?? ''}';

          context.showBarMessage(
            notificationMessage,
          );
        },
      );
      return () {};
    }, []);

    final formKey = useMemoized(() => GlobalKey<FormState>());

    return Scaffold(
      body: Form(
        key: formKey,
        child: SafeArea(
          child: Center(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(AppSpaces.largePadding),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  //! Login Title
                  context.tr.login.baseText(context, context.whiteHeadLine),

                  context.xLargeGap,

                  CircleAvatar(
                    maxRadius: 100,
                    backgroundColor: Colors.grey.withOpacity(0.1),
                    child: Image.asset(
                      'assets/images/logo.png',
                      width: 300,
                      fit: BoxFit.cover,
                    ),
                  ),

                  context.xxLargeGap,

                  //! Login Fields
                  _LoginFields(
                    emailController: emailController,
                    passwordController: passwordController,
                  ),

                  context.xLargeGap,

                  //! Login Button
                  _LoginButton(
                    formKey: formKey,
                    emailController: emailController,
                    passwordController: passwordController,
                  ),

                  context.smallGap,

                  //? Register Button
                  TextButton(
                    onPressed: () => context.to(const RegisterScreen()),
                    child: context.tr.newRegister.baseText(
                        context,
                        context.whiteTitle.copyWith(
                          color: ColorManager.primaryColor,
                          fontWeight: FontWeight.bold,
                        )),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
