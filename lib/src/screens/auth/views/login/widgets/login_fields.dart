part of login;

class _LoginFields extends StatelessWidget {
  final TextEditingController emailController;
  final TextEditingController passwordController;

  const _LoginFields(
      {required this.emailController, required this.passwordController});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        //! Email
        BaseTextField(
          label: context.tr.email,
          controller: emailController,
        ),

        context.largeGap,

        //! Password
        BaseTextField(
          label: context.tr.password,
          controller: passwordController,
          isObscure: true,
        ),
      ],
    );
  }
}
