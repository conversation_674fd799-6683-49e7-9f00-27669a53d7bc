import 'package:flutter/material.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/core/theme/color_manager.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model_helper.dart';
import 'package:opti4t_tasks/src/screens/clients/models/client_model.dart';
import 'package:opti4t_tasks/src/screens/clients/view/clients/widgets/client_card/widgets/approve_request_dialog/approve_request_dialog.dart';
import 'package:opti4t_tasks/src/screens/clients/view/clients/widgets/client_card/widgets/request_card/attachment_widget.dart';
import 'package:opti4t_tasks/src/screens/clients/view/clients/widgets/dialog/delete_request_dialog.dart';
import 'package:xr_helper/xr_helper.dart';

import 'request_card_actions.dart';

class ClientRequestsCardWidget extends StatelessWidget {
  final ClientRequestModel request;
  final bool fromFiltered;
  final int clientRequestIndex;

  const ClientRequestsCardWidget({
    super.key,
    required this.request,
    this.fromFiltered = false,
    required this.clientRequestIndex,
  });

  @override
  Widget build(BuildContext context) {
    return Dismissible(
      key: ValueKey(request.id),
      confirmDismiss: (direction) async {
        if (UserModelHelper.isManager() && !fromFiltered) {
          return await showDialog(
              context: context,
              builder: (_) => DeleteRequestDialog(request: request));
        } else {
          return false;
        }
      },
      child: Container(
        margin: const EdgeInsets.only(
            bottom: AppSpaces.mediumPadding,
            right: AppSpaces.smallPadding,
            left: AppSpaces.smallPadding),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppRadius.smallRadius),
            border: Border.all(
              color: ColorManager.secondaryColor.withOpacity(.1),
              width: 1,
            )),
        child: Stack(
          children: [
            ListTile(
              title: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  //? Date
                  Text(
                    request.createdAt?.toDate().formatDateToStringWithTime ??
                        '',
                    style: context.whiteHint.copyWith(
                      fontSize: 13,
                    ),
                  ),

                  context.smallGap,

                  Text(
                    '$clientRequestIndex. ${request.description}',
                    style: context.whiteTitle,
                  ),
                ],
              ),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (UserModelHelper.isAdmin())
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          vertical: AppSpaces.smallPadding),
                      child: Row(
                        children: [
                          Text(
                            '${context.tr.employee}: ',
                            style: context.whiteLabelLarge,
                          ),
                          context.xSmallGap,
                          Expanded(
                            child: Text(
                              request.userName.isEmpty
                                  ? context.tr.notAssigned
                                  : request.userName,
                              style: context.whiteLabelLarge.copyWith(
                                fontWeight: FontWeight.bold,
                                color: ColorManager.primaryColor,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        vertical: AppSpaces.xSmallPadding),
                    child: Row(
                      children: [
                        Text(
                          '${context.tr.assignedBy}: ',
                          style: context.whiteLabelLarge,
                        ),
                        context.xSmallGap,
                        Expanded(
                          child: Text(
                            request.adminName ?? '-',
                            style: context.whiteLabelLarge.copyWith(
                              fontWeight: FontWeight.bold,
                              color: ColorManager.primaryColor,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  AttachmentWidget(
                    attachmentUrl: request.attachmentUrl,
                  ),
                ],
              ),
              trailing: Padding(
                padding: const EdgeInsets.only(top: AppSpaces.mediumPadding),
                child: RequestCardActions(
                    request: request, fromFiltered: fromFiltered),
              ),
            ),

            //? Approve Text Button
            if (UserModelHelper.isManager())
              Positioned(
                bottom: -3,
                left: context.isAppEnglish ? null : 12,
                right: context.isAppEnglish ? 12 : null,
                child: TextButton(
                  onPressed: () => request.isApproved == true
                      ? context.showBarMessage(context.tr.alreadyApproved)
                      : showDialog(
                          context: context,
                          builder: (_) => ApproveRequestDialog(
                                request: request,
                              )),
                  child: Text(
                    request.isApproved == true
                        ? context.tr.approved
                        : context.tr.approve,
                    style: context.subTitle.copyWith(
                      color: ColorManager.successColor,
                      fontWeight: FontWeight.bold,
                      decoration: request.isApproved == true
                          ? TextDecoration.lineThrough
                          : TextDecoration.none,
                      decorationColor: ColorManager.successColor,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
