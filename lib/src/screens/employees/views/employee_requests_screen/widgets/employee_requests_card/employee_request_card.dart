import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/core/theme/color_manager.dart';
import 'package:opti4t_tasks/src/screens/clients/view/clients/widgets/client_card/widgets/request_card/attachment_widget.dart';
import 'package:opti4t_tasks/src/screens/employees/models/employee_request_model.dart';
import 'package:opti4t_tasks/src/screens/employees/views/employee_requests_screen/widgets/employee_requests_card/widgets/request_card/request_card_actions.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../auth/models/user_model_helper.dart';
import '../dialog/delete_employee_request_dialog.dart';

class EmployeeRequestCard extends ConsumerWidget {
  final EmployeeRequestModel employeeRequest;

  const EmployeeRequestCard({
    super.key,
    required this.employeeRequest,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final endDate = employeeRequest.endDate ?? DateTime.now();

    final isRequestClosedBeforeEndDate =
        employeeRequest.closedAt?.toDate().isBefore(endDate) ?? false;

    final isRequestClosedAtSameEndDay =
        employeeRequest.closedAt?.toDate().isSameDay(endDate) ?? false;

    final isRequestClosedAtAfterEndDayByDifference1Day =
        (employeeRequest.closedAt?.toDate().isAfter(endDate) ?? false) &&
            (employeeRequest.closedAt?.toDate().difference(endDate).inDays ==
                1);

    final evaluationText = isRequestClosedBeforeEndDate
        ? context.tr.excellent
        : isRequestClosedAtSameEndDay
            ? context.tr.excellent
            : isRequestClosedAtAfterEndDayByDifference1Day
                ? context.tr.good
                : context.tr.bad;

    final evaluationColor = isRequestClosedBeforeEndDate
        ? ColorManager.successColor
        : isRequestClosedAtSameEndDay
            ? ColorManager.successColor
            : isRequestClosedAtAfterEndDayByDifference1Day
                ? ColorManager.primaryColor
                : ColorManager.errorColor;

    final workDays = (employeeRequest.date
                    ?.difference(
                        (employeeRequest.closedAt?.toDate()) ?? endDate)
                    .inDays ??
                0)
            .abs() +
        1;

    final canShowEvaluation = employeeRequest.isCompleted &&
        employeeRequest.closedAt != null &&
        employeeRequest.endDate != null;

    return Dismissible(
      key: ValueKey(employeeRequest.id),
      confirmDismiss: (direction) async {
        if (kDebugMode) {
          if (UserModelHelper.isManager() ||
              (!UserModelHelper.isAdmin()) &&
                  employeeRequest.isSuggestion &&
                  !employeeRequest.isApprovedSuggestion) {
            return await showDialog(
                context: context,
                builder: (_) => DeleteEmployeeRequestDialog(
                    request: employeeRequest, isEmployeeRequest: true));
          } else {
            return false;
          }
        }

        return false;
      },
      child: Container(
        margin: const EdgeInsets.only(
          bottom: AppSpaces.smallPadding,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppRadius.smallRadius),
          color: ColorManager.secondaryColor.withOpacity(.1),
        ),
        padding: const EdgeInsets.all(AppSpaces.mediumPadding),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      //? Date
                      Row(
                        children: [
                          Text(
                            employeeRequest.createdAt
                                    ?.toDate()
                                    .formatDateToStringWithTime ??
                                '',
                            style: context.whiteHint.copyWith(
                              fontSize: 13,
                            ),
                          ),
                          if (employeeRequest.isSuggestion &&
                              !employeeRequest.isApprovedSuggestion)
                            Flexible(
                              child: FittedBox(
                                fit: BoxFit.scaleDown,
                                child: Text(
                                  ' (${context.tr.unApprovedSuggestion}) ',
                                  style: context.whiteHint.copyWith(
                                    fontSize: 13,
                                    color: ColorManager.primaryColor,
                                  ),
                                ),
                              ),
                            ),
                          if (canShowEvaluation)
                            Text(
                              ' ($evaluationText) ',
                              style: context.whiteHint.copyWith(
                                fontSize: 13,
                                color: evaluationColor,
                              ),
                            ),
                        ],
                      ),

                      context.smallGap,

                      Row(
                        children: [
                          const CircleAvatar(
                            radius: 15,
                            backgroundColor: ColorManager.primaryColor,
                            child: Icon(
                              Icons.person,
                              color: Colors.white,
                              size: 17,
                            ),
                          ),
                          context.smallGap,
                          Expanded(
                            child: Text(
                              employeeRequest.userName,
                              style: context.whiteTitle.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),

                      context.xSmallGap,

                      _TitleSubtitleRow(
                        title: context.tr.clientName,
                        subtitle: employeeRequest.clientName,
                      ),

                      context.xSmallGap,

                      _TitleSubtitleRow(
                        title: context.tr.project,
                        subtitle: employeeRequest.projectName,
                      ),

                      context.xSmallGap,

                      _TitleSubtitleRow(
                        title: context.tr.workDate,
                        subtitle: employeeRequest.date?.formatDateToString,
                      ),

                      context.xSmallGap,

                      _TitleSubtitleRow(
                        title: context.tr.expectedEndDate,
                        subtitle: employeeRequest.endDate!.formatDateToString,
                      ),

                      context.xSmallGap,

                      _TitleSubtitleRow(
                        title: context.tr.actualEndDate,
                        subtitle: employeeRequest.closedAt
                            ?.toDate()
                            .formatDateToString,
                      ),

                      context.xSmallGap,

                      _TitleSubtitleRow(
                          title: context.tr.workDays, subtitle: '$workDays'),

                      context.xSmallGap,

                      _TitleSubtitleRow(
                        title: context.tr.workTime,
                        subtitle: employeeRequest.workTime,
                      ),

                      context.xSmallGap,

                      _TitleSubtitleRow(
                        title: context.tr.priority,
                        subtitle: context.isAppEnglish
                            ? employeeRequest.priority
                            : employeeRequest.priorityAR,
                        icon: CircleAvatar(
                          radius: 7,
                          backgroundColor: employeeRequest.priority == 'High'
                              ? ColorManager.errorColor
                              : employeeRequest.priority == 'Medium'
                                  ? ColorManager.primaryColor
                                  : ColorManager.successColor,
                        ),
                      ),

                      context.xSmallGap,

                      _TitleSubtitleRow(
                        subtitle: employeeRequest.description,
                        icon: const Icon(
                          Icons.description,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                    ],
                  ),
                ),
                EmployeeRequestCardActions(
                  request: employeeRequest,
                ),
              ],
            ),
            if (employeeRequest.attachmentUrl != null &&
                employeeRequest.attachmentUrl!.isNotEmpty) ...[
              context.xSmallGap,
              AttachmentWidget(
                attachmentUrl: employeeRequest.attachmentUrl,
                width: double.infinity,
              ),
            ],
          ],
        ),
      ),
    );
  }
}

class _TitleSubtitleRow extends StatelessWidget {
  final String? title;
  final String? subtitle;
  final Widget? icon;

  const _TitleSubtitleRow(
      {super.key, this.title, required this.subtitle, this.icon});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        if (title != null) ...[
          Text(
            '$title:',
            style: context.whiteLabelLarge,
          ),
          context.xSmallGap,
        ],
        if (icon != null) ...[
          icon!,
          context.xSmallGap,
        ],
        Flexible(
          child: Text(
            subtitle == null || subtitle!.isEmpty ? '-' : subtitle!,
            style: context.whiteLabelLarge.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ],
    );
  }
}
