import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/core/shared_widgets/shared_widgets.dart';
import 'package:opti4t_tasks/src/core/theme/color_manager.dart';
import 'package:opti4t_tasks/src/screens/employees/models/employee_request_model.dart';
import 'package:opti4t_tasks/src/screens/employees/providers/employee_requests_providers.dart';
import 'package:xr_helper/xr_helper.dart';

class ApproveRequestFloatingButton extends ConsumerWidget {
  final EmployeeRequestModel request;

  const ApproveRequestFloatingButton({
    super.key,
    required this.request,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return CircleAvatar(
        backgroundColor: request.isApproved == true
            ? ColorManager.primaryColor
            : ColorManager.secondaryColor,
        child: Icon(
          CupertinoIcons.text_badge_checkmark,
          color: Colors.white,
        )).onTapWithRipple(() {
      if (request.isApproved == true) {
        context.showBarMessage(context.tr.requestAlreadyApproved,
            isError: true);
        return;
      }

      showDialog(
          context: context,
          builder: (_) => ApproveRequestDialog(request: request));
    }).paddingSymmetric(
      horizontal: AppSpaces.smallPadding,
    );
  }
}

class ApproveRequestDialog extends HookConsumerWidget {
  final EmployeeRequestModel request;

  const ApproveRequestDialog({
    super.key,
    required this.request,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final requestController =
        ref.watch(employeeRequestControllerNotifierProvider(context));

    final isUnApprovedSuggestions =
        request.isSuggestion && !request.isApprovedSuggestion;

    void approveRequest() async {
      if (isUnApprovedSuggestions) {
        await requestController.approveSuggestionRequest(
          request: request,
        );
      } else {
        await requestController.approveRequest(request: request);
      }
    }

    if (requestController.isLoading) {
      return const Center(
        child: LoadingWidget(),
      );
    }

    final startDate = request.date?.formatDateToString;

    final endDate = request.endDate ?? DateTime.now();

    final workDays = (request.date
                    ?.difference((request.closedAt?.toDate()) ?? endDate)
                    .inDays ??
                0)
            .abs() +
        1;

    final isRequestClosedBeforeEndDate =
        request.closedAt?.toDate().isBefore(endDate) ?? false;

    final isRequestClosedAtSameEndDay =
        request.closedAt?.toDate().isSameDay(endDate) ?? false;

    final isRequestClosedAtAfterEndDayByDifference1Day =
        (request.closedAt?.toDate().isAfter(endDate) ?? false) &&
            (request.closedAt?.toDate().difference(endDate).inDays == 1);

    final evaluationText = isRequestClosedBeforeEndDate
        ? context.tr.excellent
        : isRequestClosedAtSameEndDay
            ? context.tr.excellent
            : isRequestClosedAtAfterEndDayByDifference1Day
                ? context.tr.good
                : context.tr.bad;

    final evaluationColor = isRequestClosedBeforeEndDate
        ? ColorManager.successColor
        : isRequestClosedAtSameEndDay
            ? ColorManager.successColor
            : isRequestClosedAtAfterEndDayByDifference1Day
                ? ColorManager.primaryColor
                : ColorManager.errorColor;

    final canShowEvaluation = request.isCompleted &&
        request.closedAt != null &&
        request.endDate != null;

    return Dialog(
        child: Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const CircleAvatar(
              backgroundColor: ColorManager.primaryColor,
              child: Icon(
                Icons.playlist_add_check_outlined,
                color: Colors.white,
              ),
            ),
            context.mediumGap,
            Text(
              isUnApprovedSuggestions
                  ? context.tr.approveSuggestion
                  : context.tr.approveRequest,
              style: context.whiteTitle,
            ),
          ],
        ),
        context.largeGap,
        Text(
          isUnApprovedSuggestions
              ? context.tr.areYouSureYouWantToApproveThisSuggestion
              : context.tr.areYouSureYouWantToApproveThisRequest,
          style: context.whiteSubTitle,
        ),

        context.largeGap,

        // Work Days
        if (canShowEvaluation)
          Container(
            padding: EdgeInsets.all(AppSpaces.mediumPadding),
            decoration: BoxDecoration(
              color: ColorManager.primaryColor,
              borderRadius: BorderRadius.circular(AppRadius.mediumRadius),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.date_range,
                    ),
                    context.smallGap,
                    Text(
                      '${context.tr.startDate}: $startDate',
                      style: context.whiteSubTitle,
                    ),
                  ],
                ),
                context.mediumGap,
                Row(
                  children: [
                    Icon(
                      Icons.date_range,
                    ),
                    context.smallGap,
                    Expanded(
                      child: Text(
                        '${context.tr.expectedEndDate}: ${endDate.formatDateToString}',
                        style: context.whiteSubTitle,
                      ),
                    )
                  ],
                ),
                context.mediumGap,
                Row(
                  children: [
                    Icon(
                      Icons.date_range,
                    ),
                    context.smallGap,
                    Expanded(
                      child: Text(
                        '${context.tr.actualEndDate}: ${request.closedAt?.toDate().formatDateToString}',
                        style: context.whiteSubTitle,
                      ),
                    )
                  ],
                ),
                context.mediumGap,
                Row(
                  children: [
                    Icon(
                      Icons.calendar_view_day,
                    ),
                    context.smallGap,
                    Text(
                      '${context.tr.workDays}: $workDays',
                      style: context.whiteSubTitle,
                    ),
                  ],
                ),
                context.mediumGap,
                Row(
                  children: [
                    CircleAvatar(
                      radius: 14,
                      backgroundColor: evaluationColor,
                      child: Icon(
                        CupertinoIcons.checkmark_seal_fill,
                        color: Colors.white,
                        size: 17,
                      ),
                    ),
                    context.smallGap,
                    Text(
                      '${context.tr.evaluation}: $evaluationText',
                      style: context.whiteSubTitle,
                    ),
                  ],
                ),
              ],
            ),
          ),

        context.xLargeGap,
        Row(
          children: [
            Expanded(
              child: TextButton(
                onPressed: () => context.back(),
                child: Text(context.tr.cancel, style: context.whiteSubTitle),
              ),
            ),
            context.mediumGap,
            Expanded(
                flex: 2,
                child: Button(
                  label: context.tr.approveRequest,
                  onPressed: approveRequest,
                  textColor: Colors.white,
                )),
          ],
        ).sized(
          height: 45,
        )
      ],
    ).paddingAll(AppSpaces.largePadding - 4));
  }
}
