import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti4t_tasks/src/core/consts/app_constants.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/core/theme/color_manager.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model_helper.dart';
import 'package:opti4t_tasks/src/screens/employees/models/employee_request_model.dart';
import 'package:opti4t_tasks/src/screens/employees/views/employee_requests_screen/widgets/employee_requests_card/employee_request_card.dart';
import 'package:xr_helper/xr_helper.dart';

class EmployeeRequestListWidget extends HookConsumerWidget {
  final List<EmployeeRequestModel> employeeRequests;
  final ValueNotifier<UserModel?>? selectedEmployee;

  const EmployeeRequestListWidget({
    super.key,
    required this.employeeRequests,
    required this.selectedEmployee,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Check if all parent employeeRequests have no requests

    // If all parent employeeRequests have no requests and fromHome is true, return "No Requests" message
    if (employeeRequests.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.only(top: 120),
          child: Column(
            children: [
              CircleAvatar(
                backgroundColor: ColorManager.primaryColor,
                radius: 40,
                child: Icon(
                  Icons.assignment,
                  color: Colors.white,
                  size: 40,
                ),
              ),
              context.mediumGap,
              Text(
                context.tr.noRequests,
                style: context.whiteTitle.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return ListView.builder(
        shrinkWrap: true,
        itemCount: employeeRequests.length,
        physics: const NeverScrollableScrollPhysics(),
        padding: EdgeInsets.only(
          bottom: UserModelHelper.isManager()
              ? AppSpaces.xLargePadding + 15
              : AppSpaces.largePadding,
          right: AppSpaces.mediumPadding,
          left: AppSpaces.mediumPadding,
        ),
        itemBuilder: (context, index) {
          return WidgetAnimator(
            delay: Duration(milliseconds: AppConsts.animatedDuration * index),
            duration: const Duration(milliseconds: AppConsts.animatedDuration),
            child: EmployeeRequestCard(
              employeeRequest: employeeRequests[index],
            ),
          ).paddingSymmetric(
            vertical: AppSpaces.smallPadding,
          );
        });
  }
}
