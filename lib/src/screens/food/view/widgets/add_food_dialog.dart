import 'package:connectify_app/src/screens/food/controller/food_controller.dart';
import 'package:connectify_app/src/screens/food/view/widgets/add_food_widgets.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../shared/widgets/shared_widgets.dart';

Future<void> showAddFoodDialog(context) {
  return showDialog(
    context: context,
    builder: (context) {
      return HookConsumer(
        builder: (context, ref, child) {
          final foodChangeNotifierCtrl =
              ref.watch(foodChangeNotifierControllerProvider(context));

          final valueNotifiers = {
            ApiStrings.students: useState<List<StudentModel>>([]),
            ApiStrings.food: useState<int>(0),
            ApiStrings.type: useState<int>(0),
          };

          //!-----------------------------------------------------

          final formKey = useState(GlobalKey<FormState>());

          //!-----------------------------------------------------

          Future<void> addFood() async {
            if (!formKey.value.currentState!.validate()) return;
            final students = valueNotifiers[ApiStrings.students]!.value
                as List<StudentModel>;
            await Future.forEach(
              students,
              (student) async {
                await foodChangeNotifierCtrl.addFood(
                  mealTypeValue: valueNotifiers[ApiStrings.food]?.value as int,
                  mealAmountValue:
                      valueNotifiers[ApiStrings.type]?.value as int,
                  student: student,
                );
              },
            );

            if (!context.mounted) return;
            context.back();
            context.showBarMessage(context.tr.addedSuccessfully);
          }

          return AlertDialogWidget(
              header: context.tr.food,
              isLoading: foodChangeNotifierCtrl.isLoading,
              isImage: false,
              child: Form(
                key: formKey.value,
                child: AddFoodWidgets(
                  valueNotifiers: valueNotifiers,
                ),
              ),
              onConfirm: () async => await addFood());
        },
      );
    },
  );
}
