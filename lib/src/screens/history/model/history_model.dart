import 'package:collection/collection.dart';
import 'package:connectify_app/src/screens/Activities/models/teacher_activity_model.dart';
import 'package:connectify_app/src/screens/activity_level/model/activity_level_model.dart';
import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/food/model/food_model.dart';
import 'package:connectify_app/src/screens/mood/model/mood_model.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model.dart';
import 'package:connectify_app/src/screens/sleep/model/sleep_model.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/screens/supplies/model/supply_model.dart';
import 'package:connectify_app/src/screens/teacher/models/teacher_model.dart';
import 'package:connectify_app/src/screens/toilet/model/toilet_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:equatable/equatable.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../nursery/models/nursery_model_helper.dart';

List<HistoryModel> responseToHistoryModelList(response) {
  final data = (response[ApiStrings.data] as List?) ?? [];

  final historyData = data.map((e) => HistoryModel.fromJson(e)).toList();

  return historyData;
}

enum HistoryType {
  activity,
  sleep,
  food,
  supply,
  toilet,
  activityLevel,
  mood,
}

class HistoryModel extends Equatable {
  final int? id;
  final HistoryType historyType;
  final StudentModel? student;
  final NurseryModel? nursery;
  final TeacherModel? teacher;
  final TeacherActivityModel? activity;
  final SleepModel? sleep;
  final FoodModel? food;
  final SupplyModel? supply;
  final ToiletModel? toilet;
  final String date;
  final DateTime? createdAt;

  const HistoryModel({
    this.id,
    this.historyType = HistoryType.activity,
    this.student,
    this.nursery,
    this.teacher,
    this.activity,
    this.sleep,
    this.food,
    this.supply,
    this.toilet,
    this.date = '',
    this.createdAt,
  });

  factory HistoryModel.fromJson(Map<String, dynamic> json) {
    final attributes = json[ApiStrings.attributes];
    return HistoryModel(
      id: json[ApiStrings.id],
      historyType: HistoryType.values.firstWhereOrNull(
            (e) => e.name == attributes[ApiStrings.type],
          ) ??
          HistoryType.activity,
      student: attributes[ApiStrings.student][ApiStrings.data] != null
          ? StudentModel.fromJson(
              attributes[ApiStrings.student][ApiStrings.data])
          : null,
      teacher: attributes[ApiStrings.teacher][ApiStrings.data] != null
          ? TeacherModel.fromJson(
              attributes[ApiStrings.teacher][ApiStrings.data])
          : null,
      activity: attributes[ApiStrings.activity][ApiStrings.data] != null
          ? TeacherActivityModel.fromJson(
              attributes[ApiStrings.activity][ApiStrings.data])
          : null,
      sleep: attributes[ApiStrings.sleep][ApiStrings.data] != null
          ? SleepModel.fromJson(attributes[ApiStrings.sleep][ApiStrings.data])
          : null,
      food: attributes[ApiStrings.food][ApiStrings.data] != null
          ? FoodModel.fromJson(attributes[ApiStrings.food][ApiStrings.data])
          : null,
      supply: attributes[ApiStrings.supply][ApiStrings.data] != null
          ? SupplyModel.fromJson(attributes[ApiStrings.supply][ApiStrings.data])
          : null,
      toilet: attributes[ApiStrings.toilet][ApiStrings.data] != null
          ? ToiletModel.fromJson(attributes[ApiStrings.toilet][ApiStrings.data])
          : null,
      date: attributes[ApiStrings.date] ?? '',
      createdAt: attributes[ApiStrings.createdAt] != null
          ? DateTime.parse(attributes[ApiStrings.createdAt]).toUtc().toLocal()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) ApiStrings.id: id,
      ApiStrings.student: student?.id,
      ApiStrings.activity: activity?.id,
      ApiStrings.sleep: sleep?.id,
      ApiStrings.food: food?.id,
      ApiStrings.supply: supply?.id,
      ApiStrings.toilet: toilet?.id,
      ApiStrings.type: historyType.name,
      ApiStrings.date: DateTime.now().formatDateToString,
      ApiStrings.teacher: const UserModel().currentUser.id,
      ApiStrings.nursery: NurseryModelHelper.currentNurseryId(),
    };
  }

  @override
  List<Object?> get props => [
        id,
        historyType,
        student,
        nursery,
        teacher,
        activity,
        sleep,
        food,
        supply,
        toilet,
        date,
        createdAt,
      ];
}
