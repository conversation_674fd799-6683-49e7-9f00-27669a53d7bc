import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/core/services/app_settings/controller/settings_controller.dart';
import 'package:opti4t_tasks/src/core/shared_widgets/drop_downs/language_drop_down.dart';
import 'package:opti4t_tasks/src/core/theme/color_manager.dart';
import 'package:opti4t_tasks/src/screens/auth/controllers/auth_controller.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model_helper.dart';
import 'package:xr_helper/xr_helper.dart';

class HomeTopSection extends ConsumerWidget {
  final String? title;
  const HomeTopSection({super.key, this.title});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final user = UserModelHelper.signedUser();

    final settingsController = ref.watch(settingsControllerProvider);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        if (title != null) ...[
          Text(
            title!,
            style: context.headLine.copyWith(
              color: ColorManager.primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ] else
          Row(
            children: [
              FittedBox(
                fit: BoxFit.scaleDown,
                alignment: settingsController.isEnglish
                    ? Alignment.centerLeft
                    : Alignment.centerRight,
                child: Text(
                  '${context.tr.welcome}, ${user.name}',
                  style: context.whiteTitle.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ).sized(
                width: context.width * 0.6,
              ),
            ],
          ),

        const Spacer(),

        //! Change Language
        IconButton(
            onPressed: () => showDialog(
                context: context, builder: (_) => const LanguageDropDown()),
            icon: CircleAvatar(
                backgroundColor: ColorManager.secondaryColor,
                child: const Icon(Icons.language, color: Colors.white))),

        context.smallGap,

        CircleAvatar(
          backgroundColor: ColorManager.errorColor,
          child: IconButton(
            onPressed: () {
              showDialog(
                  context: context, builder: (_) => const _LogoutDialog());
            },
            icon: const Icon(
              Icons.logout,
              color: Colors.white,
            ),
          ),
        )
      ],
    ).paddingAll(AppSpaces.mediumPadding);
  }
}

class _LogoutDialog extends HookConsumerWidget {
  const _LogoutDialog();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authController = ref.watch(authControllerNotifierProvider(context));

    void logout() async {
      await authController.logout();
    }

    return Dialog(
        child: Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const CircleAvatar(
              backgroundColor: ColorManager.errorColor,
              child: Icon(
                Icons.logout,
                color: Colors.white,
              ),
            ),
            context.mediumGap,
            Text(
              context.tr.logout,
              style: context.whiteTitle,
            ),
          ],
        ),

        context.largeGap,

        //? Are you sure you want to logout?
        Text(
          context.tr.areYouSureYouWantToLogout,
          style: context.whiteSubTitle,
        ),

        context.xLargeGap,

        Row(
          children: [
            //? Cancel Button
            Expanded(
              child: TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(context.tr.cancel, style: context.whiteSubTitle),
              ),
            ),

            context.mediumGap,

            Expanded(
                flex: 2,
                child: Button(
                  label: context.tr.logout,
                  onPressed: logout,
                  textColor: Colors.white,
                )),
          ],
        ).sized(
          height: 45,
        )
      ],
    ).paddingAll(AppSpaces.mediumPadding));
  }
}
