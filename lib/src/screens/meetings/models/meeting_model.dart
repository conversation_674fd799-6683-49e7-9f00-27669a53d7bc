import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model_helper.dart';
import 'package:xr_helper/xr_helper.dart';

class MeetingModel extends Equatable {
  final String? id;
  final String title;
  final String description;
  final String participants;
  final String uid;
  final String userName;
  final String userEmail;
  final DateTime? date;
  final Timestamp? createdAt;
  final bool? isApproved;

  const MeetingModel({
    this.id,
    this.title = '',
    this.description = '',
    this.participants = '',
    this.uid = '',
    this.userEmail = '',
    this.userName = '',
    this.date,
    this.createdAt,
    this.isApproved,
  });

  @override
  List<Object?> get props => [
        id,
        title,
        description,
    participants,
        uid,
        userEmail,
        userName,
        createdAt,
        date,
        isApproved,
      ];

  factory MeetingModel.fromJson(String id,
      {required Map<String, dynamic> data}) {
    return MeetingModel(
      id: id,
      title: data['title'] ?? '',
      description: data['description'] ?? '',
      participants: data['participants'] ?? '',
      uid: data['uid'] ?? '',
      userEmail: data['user_email'] ?? '',
      userName: data['user_name'] ?? '',
      date: DateTime.tryParse(data['date'] ?? ''),
      createdAt: data['createdAt'] as Timestamp?,
      isApproved: data['is_approved'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'uid': UserModelHelper.localUID(),
      'user_email': UserModelHelper.signedUser().email,
      'user_name': UserModelHelper.signedUser().name,
      'participants': participants,
      'title': title,
      'description': description,
      'date': date?.formatDateToString ?? '',
      'createdAt': Timestamp.now(),
      if (isApproved != null) 'is_approved': isApproved,
    };
  }

  //? To Approve Json
  Map<String, dynamic> toJsonApprove() {
    return {
      'is_approved': true,
      'description': description,
    };
  }

  //? To Reject Json
  Map<String, dynamic> toJsonReject() {
    return {
      'is_approved': false,
      'description': description,
    };
  }

  MeetingModel copyWith({
    String? id,
    String? title,
    String? description,
    String? participants,
    String? uid,
    String? userEmail,
    String? userName,
    DateTime? date,
    DateTime? toDate,
    Timestamp? createdAt,
    bool? isApproved,
    String? partFromTime,
    String? partToTime,
    bool? isPartMeeting,
  }) {
    return MeetingModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      participants: participants ?? this.participants,
      uid: uid ?? this.uid,
      userEmail: userEmail ?? this.userEmail,
      userName: userName ?? this.userName,
      date: date ?? this.date,
      createdAt: createdAt ?? this.createdAt,
      isApproved: isApproved ?? this.isApproved,
    );
  }

  factory MeetingModel.empty() {
    return const MeetingModel(id: null);
  }
}
