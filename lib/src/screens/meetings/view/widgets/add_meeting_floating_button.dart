import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti4t_tasks/src/core/extensions/context_extensions.dart';
import 'package:opti4t_tasks/src/core/shared_widgets/shared_widgets.dart';
import 'package:opti4t_tasks/src/screens/auth/models/user_model_helper.dart';
import 'package:opti4t_tasks/src/screens/meetings/models/meeting_model.dart';
import 'package:opti4t_tasks/src/screens/meetings/providers/meetings_providers.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../core/theme/color_manager.dart';

class AddMeetingFloatingButton extends ConsumerWidget {
  final MeetingModel? meeting;

  const AddMeetingFloatingButton({
    super.key,
    this.meeting,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return FloatingActionButton(
      onPressed: () => showDialog(
          context: context,
          builder: (_) => AddMeetingDialog(
                meeting: meeting,
              )),
      child: const Icon(Icons.add),
    );
  }
}

class AddMeetingDialog extends HookConsumerWidget {
  final MeetingModel? meeting;

  const AddMeetingDialog({
    super.key,
    this.meeting,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final meetingController =
        ref.watch(meetingControllerNotifierProvider(context));

    final isEdit = meeting != null;

    final titleController = useTextEditingController(text: meeting?.title);
    final descriptionController =
        useTextEditingController(text: meeting?.description ?? '');
    final participantsController =
        useTextEditingController(text: meeting?.participants ?? '');

    final selectedDate = useState<DateTime>(meeting?.date ?? DateTime.now());

    final formKey = useState(GlobalKey<FormState>());

    void validateAndAddMeeting() async {
      if (formKey.value.currentState!.validate()) {
        final meetingData = MeetingModel(
          id: isEdit ? meeting!.id : null,
          title: titleController.text,
          description: descriptionController.text,
          participants: participantsController.text,
          uid: UserModelHelper.localUID(),
          date: selectedDate.value,
        );

        if (isEdit) {
          meetingController.updateMeeting(meeting: meetingData);
        } else {
          meetingController.addMeeting(meeting: meetingData);
        }
      }
    }

    if (meetingController.isLoading) {
      return const Center(
        child: LoadingWidget(),
      );
    }

    return Dialog(
        child: Form(
      key: formKey.value,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(AppSpaces.largePadding - 4),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const CircleAvatar(
                  backgroundColor: ColorManager.primaryColor,
                  child: Icon(
                    Icons.calendar_today,
                    color: Colors.white,
                  ),
                ),
                context.mediumGap,
                Text(
                  isEdit ? context.tr.editMeeting : context.tr.addMeeting,
                  style: context.whiteTitle,
                ),
              ],
            ),

            // SwitchListTile(
            //   title:
            //       Text(context.tr.partMeeting, style: context.whiteSubTitle),
            //   value: isPartMeeting.value,
            //   onChanged: (value) {
            //     isPartMeeting.value = value;
            //   },
            // ),

            context.largeGap,

            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(context.tr.date, style: context.whiteSubTitle),
                context.smallGap,
                //! From Date
                BaseDatePicker(
                  selectedDateNotifier: selectedDate,
                  label: context.tr.date,
                  isWhite: true,
                  align: context.isAppEnglish
                      ? Alignment.centerLeft
                      : Alignment.centerRight,
                ),
              ],
            ),

            context.largeGap,

            //! Title
            BaseTextField(
              label: context.tr.title,
              isRequired: false,
              isWhiteText: true,
              controller: titleController,
              minLines: 1,
              maxLines: 5,
              textInputType: TextInputType.multiline,
              validator: (value) {
                return Validations.mustBeNotEmpty(value,
                    emptyMessage: context.tr.fieldCannotBeEmpty);
              },
            ),

            context.largeGap,

            //! Participants
            BaseTextField(
              label: context.tr.participants,
              isRequired: false,
              isWhiteText: true,
              controller: participantsController,
              hint: context.isEng ? 'e.g. Mohamed, Ahmed' : 'مثال: محمد, أحمد',
              minLines: 1,
              maxLines: 5,
              textInputType: TextInputType.multiline,
              validator: (value) {
                return Validations.mustBeNotEmpty(value,
                    emptyMessage: context.tr.fieldCannotBeEmpty);
              },
            ),

            context.largeGap,

            //! Description
            BaseTextField(
              label: context.tr.description,
              isRequired: false,
              isWhiteText: true,
              controller: descriptionController,
              minLines: 1,
              maxLines: 5,
              textInputType: TextInputType.multiline,
              validator: (value) {
                return Validations.mustBeNotEmpty(value,
                    emptyMessage: context.tr.fieldCannotBeEmpty);
              },
            ),

            context.largeGap,
            Button(
              label: context.tr.submit,
              onPressed: validateAndAddMeeting,
              textColor: Colors.white,
            )
          ],
        ),
      ),
    ));
  }
}
