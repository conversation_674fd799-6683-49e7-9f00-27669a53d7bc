import 'package:connectify_app/src/screens/mood/repo/mood_repo.dart';
import 'package:connectify_app/src/screens/notification/model/notification_model.dart';
import 'package:connectify_app/src/screens/notification/repo/notification_repo.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../model/mood_model.dart';

final moodControllerProvider =
    Provider.family<MoodController, BuildContext>((ref, context) {
  final moodRepo = ref.watch(moodRepoProvider);

  return MoodController(moodRepo: moodRepo, context: context);
});

final moodChangeNotifierControllerProvider =
    ChangeNotifierProvider.family<MoodController, BuildContext>((ref, context) {
  final moodRepo = ref.watch(moodRepoProvider);

  return MoodController(moodRepo: moodRepo, context: context);
});

final getAllMoodData =
    FutureProvider.family<List<MoodModel>, BuildContext>((ref, context) {
  final moodRepo = ref.watch(moodRepoProvider);

  final moodController = MoodController(moodRepo: moodRepo, context: context);

  return moodController.getMoodData();
});

class MoodController extends BaseVM {
  final MoodRepo moodRepo;
  final BuildContext context;

  MoodController({required this.moodRepo, required this.context});

  Future<List<MoodModel>> getMoodData() async {
    return await baseFunction(context, () async {
      final moodData = await moodRepo.getMoodData();

      return moodData;
    });
  }

  //? Add Mood ========================================================
  Future<void> addMood(
      {required int moodValue,
      required String? note,
      required final StudentModel? student}) async {
    return await baseFunction(context, () async {
      final getMoodCurrentVal =
          MoodModel.getMoodValue(value: moodValue);

      final mood = MoodModel(
          mood: getMoodCurrentVal,
          note: note,
          student: student);

      await moodRepo.addMood(mood: mood);

      NotificationService.sendNotification(
        title: "Mood Update",
        body:
            "Mood (${mood.mood?.name}) has been added to ${student?.name}'s record",
        userTokenOrTopic: NurseryModelHelper.parentByStudentTopic(student?.id),
        isTopic: true,
      );

      postNewNotification(
          notificationModel: NotificationModel(
        title: "Mood Update",
        body:
            "Mood (${mood.mood?.name}) has been added to ${student?.name}'s record",
        topic: NurseryModelHelper.parentByStudentTopic(student?.id),
      ));
    });
  }
}
