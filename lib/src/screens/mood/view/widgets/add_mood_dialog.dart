import 'dart:developer';

import 'package:connectify_app/src/screens/mood/controller/mood_controller.dart';
import 'package:connectify_app/src/screens/mood/view/widgets/add_mood_widgets.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../shared/widgets/shared_widgets.dart';

Future<void> showAddMoodDialog(context) {
  return showDialog(
    context: context,
    builder: (context) {
      return HookConsumer(
        builder: (context, ref, child) {
          final moodChangeNotifierCtrl =
              ref.watch(moodChangeNotifierControllerProvider(context));

          final valueNotifiers = {
            ApiStrings.students: useState<List<StudentModel>>([]),
            ApiStrings.mood: useState<int>(0),
            ApiStrings.note: useState<String>(''),
          };

          //!-----------------------------------------------------

          final formKey = useState(GlobalKey<FormState>());

          //!-----------------------------------------------------

          Future<void> addMood() async {
            if (!formKey.value.currentState!.validate()) return;
            final students = valueNotifiers[ApiStrings.students]!.value
                as List<StudentModel>;
            log('Mood Students: $students');
            await Future.forEach(
              students,
              (student) async {
                await moodChangeNotifierCtrl.addMood(
                  moodValue: valueNotifiers[ApiStrings.mood]?.value as int,
                  note: valueNotifiers[ApiStrings.note]?.value as String?,
                  student: student,
                );
              },
            );

            if (!context.mounted) return;
            context.back();
            context.showBarMessage(context.tr.addedSuccessfully);
          }

          //!-----------------------------------------------------

          return AlertDialogWidget(
              header: context.tr.addMood,
              isLoading: moodChangeNotifierCtrl.isLoading,
              isImage: false,
              child: Form(
                key: formKey.value,
                child: AddMoodWidgets(
                  valueNotifiers: valueNotifiers,
                ),
              ),
              onConfirm: () async => await addMood());
        },
      );
    },
  );
}
