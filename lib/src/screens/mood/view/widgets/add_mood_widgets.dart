import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../shared/widgets/drop_downs/multi_student_drop_down.dart';
import '../../../../shared/widgets/tab_bar_widgets/add_container_widget.dart';

class AddMoodWidgets extends HookWidget {
  final Map<String, ValueNotifier> valueNotifiers;

  const AddMoodWidgets({super.key, required this.valueNotifiers});

  @override
  Widget build(BuildContext context) {
    final List<String> moodList = [
      context.tr.angry,
      context.tr.calm,
      context.tr.excited,
      context.tr.happy,
      context.tr.sad,
      context.tr.sleepy,
      context.tr.unwell,
      context.tr.worried,
    ];

    final List<String> moodImages = [
      Assets.svg.angry,
      Assets.svg.calm,
      Assets.svg.excited,
      Assets.svg.happy,
      Assets.svg.sad,
      Assets.svg.sleepy,
      Assets.svg.unwell,
      Assets.svg.worried,
    ];

    final indexMoodValue = useState<int>(0);
    final noteController = useTextEditingController();

    useEffect(() {
      valueNotifiers[ApiStrings.note]?.addListener(() {
        noteController.text = valueNotifiers[ApiStrings.note]?.value ?? '';
      });
      return null;
    }, []);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        MultiStudentDropDown(
          selectedStudents: valueNotifiers[ApiStrings.students]
              as ValueNotifier<List<StudentModel>>,
        ),
        context.mediumGap,
        Text(
          context.tr.mood,
          style: context.title,
        ),
        context.smallGap,
        SizedBox(
          height: 200,
          child: GridView.builder(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4,
              mainAxisSpacing: 10,
              crossAxisSpacing: 10,
              childAspectRatio: 0.8,
            ),
            itemCount: moodList.length,
            itemBuilder: (context, index) {
              return AddContainerWidget(
                onTap: () {
                  indexMoodValue.value = index;
                  valueNotifiers[ApiStrings.mood]?.value = index;
                },
                isSelected: indexMoodValue.value == index,
                image: moodImages[index],
                title: moodList[index],
              );
            },
          ),
        ),
        context.mediumGap,
        Text(
          context.tr.note,
          style: context.title,
        ),
        context.smallGap,
        BaseTextField(
          controller: noteController,
          label: context.tr.enterNote,
          maxLines: 3,
          onChanged: (value) {
            valueNotifiers[ApiStrings.note]?.value = value;
          },
        ),
      ],
    );
  }
}
