import 'dart:math';

class PauseTimeModel {
  final String? startPauseTime;
  final String? endPauseTime;
  final String? pauseReason;

  const PauseTimeModel({
    this.startPauseTime,
    this.endPauseTime,
    this.pauseReason,
  });

  factory PauseTimeModel.fromMap(Map<dynamic, dynamic> map) {
    return PauseTimeModel(
      startPauseTime: map['start_pause_time'],
      endPauseTime: map['end_pause_time'],
      pauseReason: map['reason'],
    );
  }

  Map<dynamic, dynamic> toMap() {
    return {
      'id': Random().nextInt(1000),
      'start_pause_time': startPauseTime,
      'end_pause_time': endPauseTime,
      'reason': pauseReason,
    };
  }
}
