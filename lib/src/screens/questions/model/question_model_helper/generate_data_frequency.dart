import 'package:flutter/material.dart';
import 'package:good_thinker/src/screens/questions/model/question_model.dart';
import 'package:good_thinker/src/screens/questions/model/question_model_helper/shared_generated_data.dart';
import 'package:good_thinker/src/screens/session/model/session_model.dart';
import 'package:good_thinker/src/shared/consts/questions_strings.dart';
import 'package:good_thinker/src/shared/utils/filtered_questions.dart';
import 'package:stop_watch_timer/stop_watch_timer.dart';

Map<String, List<Map<String, dynamic>>> generateDataFrequencyResults({
  required QuestionsModel question,
}) {
  final dataFrequencyValueNotifiers = question.dataFrequencyValueNotifiers;

  final session = question.session;

  final results = <String, List<Map<String, dynamic>>>{};

  for (var i = 0; i < dataFrequencyValueNotifiers.length; i++) {
    final dataFrequencyValueNotifier = dataFrequencyValueNotifiers[i];

    final dataFrequencyResults = <Map<String, dynamic>>[];

    // * Calculate Full Score & Make First Map -------------------------------------------
    final dataFrequencyResult = _getDataFrequencyResult(
        dataFrequencyValueNotifier.value,
        session: session,
        startTime: question.startTime,
        i: i);

    dataFrequencyResults.add(dataFrequencyResult);

    // * Add results with index to results {0: dataFrequencyResults} ---------------
    results[questionCounter.toString()] = dataFrequencyResults;

    questionCounter++;
  }

  return results;
}

//! Helper Functions ===========================================================
Map<String, dynamic> _getDataFrequencyResult(
    Map<String, dynamic> dataFrequencyValueNotifierList,
    {required SessionModel? session,
    required DateTime? startTime,
    required int i}) {
  final learningTrees = session?.learningTrees?.dataFrequencyQuestions ?? [];

  if (learningTrees.isEmpty) return {};

  final target = learningTrees[i];

  final dataFrequencyScore =
      dataFrequencyValueNotifierList[QuestionsStrings.selectedFrequency]
          as ValueNotifier<num>;

  final stopWatch =
      dataFrequencyValueNotifierList[QuestionsStrings.selectedDurationTime]
          as ValueNotifier<StopWatchTimer>;

  final durationRate = StopWatchTimer.getDisplayTime(
    stopWatch.value.rawTime.value,
    minute: true,
    second: true,
    hours: false,
    milliSecond: false,
  );

  final dataFrequencyResult = generateMainMap(1,
      fullScore: dataFrequencyScore.value,
      target: target,
      rate: durationRate,
      startTime: startTime);

  return dataFrequencyResult;
}
