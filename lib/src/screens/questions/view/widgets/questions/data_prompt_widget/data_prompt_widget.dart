library data_percent_widget;

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:good_thinker/src/screens/questions/view/widgets/questions/data_prompt_widget/body_section/data_prompt_body.dart';
import 'package:good_thinker/src/screens/questions/view/widgets/questions/data_prompt_widget/body_section/view_score_widget/view_data_prompt_widget.dart';
import 'package:good_thinker/src/screens/questions/view/widgets/questions/data_prompt_widget/bottom_section/calculate_data_prompt_score_widget.dart';
import 'package:good_thinker/src/screens/session/model/learning_tree_model.dart';
import 'package:good_thinker/src/shared/extensions/context_extensions.dart';
import 'package:good_thinker/src/shared/widgets/box_shadow.dart';
import 'package:good_thinker/src/shared/widgets/questions/top_data_section_widget.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../../shared/theme/color_manager.dart';
import '../../../../../targets/view/widgets/question_description_icon.dart';

class DataPromptWidget extends HookWidget {
  final TargetModel target;
  final ValueNotifier<List<Map<String, dynamic>>> dataPromptValueNotifiers;

  const DataPromptWidget({
    super.key,
    required this.target,
    required this.dataPromptValueNotifiers,
  });

  @override
  Widget build(BuildContext context) {
    final showScore = useState<bool>(false);

    return StatefulBuilder(builder: (context, setState) {
      return Container(
        padding: const EdgeInsets.all(AppSpaces.mediumPadding),
        width: double.infinity,
        decoration: BoxDecoration(
          color: ColorManager.white,
          borderRadius: BorderRadius.circular(AppRadius.mediumRadius),
          boxShadow: ConstantsWidgets.boxShadowFromBottom,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            //! Top Section (Title, Eye Icon, Type, Question) ====================================================
            BaseTopDataSectionWidget(
              target: target,
              showScore: showScore,
            ),

            context.mediumGap,

            //! Question ====================================================
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  context.tr.q,
                  style: context.title,
                ),
                context.xSmallGap,
                Flexible(
                    child: Text(
                  target.title ?? '',
                  style: context.title.copyWith(fontWeight: FontWeight.bold),
                )),
                context.smallGap,
                QuestionDescriptionIcon(
                  target: target,
                ),
              ],
            ),

            context.mediumGap,

            if (showScore.value)
              //! Score (Yes, No) Widgets ====================================================
              ViewDataPromptScore(
                target: target,
                dataPromptValueNotifiers: dataPromptValueNotifiers,
                setState: setState,
              ),

            DatapromptBody(
              target: target,
              showScore: showScore.value,
              dataPromptValueNotifiers: dataPromptValueNotifiers,
              setState: setState,
            ),

            context.mediumGap,

            //! View Score percent ====================================================
            CalculateDataPromptScoreWidget(
              dataPromptValueNotifiers: dataPromptValueNotifiers,
            ),
          ],
        ),
      );
    });
  }
}
