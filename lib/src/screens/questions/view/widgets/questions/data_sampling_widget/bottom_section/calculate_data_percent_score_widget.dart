// import 'package:flutter/material.dart';
// import 'package:flutter_hooks/flutter_hooks.dart';
// import 'package:good_thinker/src/shared/extensions/context_extensions.dart';
// import 'package:good_thinker/src/shared/theme/color_manager.dart';
// import 'package:good_thinker/src/shared/utils/calculate_questions_score.dart';
// import 'package:good_thinker/src/shared/utils/filtered_questions.dart';
// import 'package:xr_helper/xr_helper.dart';
//
// class CalculateDataPercentScoreWidget extends HookWidget {
//   final ValueNotifier<List<Map<String, dynamic>>> dataPercentValueNotifiers;
//
//   const CalculateDataPercentScoreWidget(
//       {super.key, required this.dataPercentValueNotifiers});
//
//   @override
//   Widget build(BuildContext context) {
//     return ValueListenableBuilder<List<Map<String, dynamic>>>(
//       valueListenable: dataPercentValueNotifiers,
//       builder: (context, value, child) {
//         return HookBuilder(builder: (context) {
//           final fullScore = useState<num>(0);
//
//           fullScore.value =
//               calculateDataPercentScore(value.unEmptyDataPercentQuestions);
//
//           return Center(
//             child: Text(
//               '${context.tr.score} (${fullScore.value.isNaN ? '0' : fullScore.value.toStringAsFixed(0)}%)',
//               style: context.labelLarge.copyWith(
//                   fontWeight: FontWeight.bold,
//                   color: ColorManager.secondaryColor),
//             ),
//           );
//         });
//       },
//     );
//   }
// }
