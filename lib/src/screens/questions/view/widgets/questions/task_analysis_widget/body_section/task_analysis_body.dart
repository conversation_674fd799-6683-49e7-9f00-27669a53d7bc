import 'package:expandable_page_view/expandable_page_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:good_thinker/src/screens/questions/view/widgets/questions/task_analysis_widget/task_analysis_chips.dart';
import 'package:good_thinker/src/screens/session/model/learning_tree_model.dart';
import 'package:good_thinker/src/shared/consts/questions_strings.dart';
import 'package:good_thinker/src/shared/extensions/context_extensions.dart';
import 'package:good_thinker/src/shared/utils/default_question_values.dart';
import 'package:good_thinker/src/shared/widgets/buttons/arrow_button_widget.dart';
import 'package:xr_helper/xr_helper.dart';

class TaskAnalysisBody extends HookWidget {
  final TargetModel target;
  final ValueNotifier<List<Map<String, dynamic>>> taskAnalysisValueNotifiers;
  final Function setState;
  final bool showScore;

  const TaskAnalysisBody(
      {super.key,
      required this.target,
      required this.taskAnalysisValueNotifiers,
      required this.setState,
      required this.showScore});

  @override
  Widget build(BuildContext context) {
    final pageController = usePageController();
    final currentTaskIndex = useState<int>(0); // Add this line

    void onNext() {
      if (currentTaskIndex.value <
          (target.targetInfo?.tasks?.length ?? 0) - 1) {
        final index = taskAnalysisValueNotifiers.value.length;

        final data = defaultTaskAnalysisMap(index + 1);

        //? Add-DataPercent
        taskAnalysisValueNotifiers.value.add(data);

        pageController.nextPage(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOutCubic);

        currentTaskIndex.value++;
      } else {
        context.showBarMessage(context.tr.questionsCompleted, isError: true);
      }

      setState(() {});
    }

    return Visibility(
      maintainState: true,
      visible: !showScore,
      child: ExpandablePageView(
        controller: pageController,
        physics: const NeverScrollableScrollPhysics(),
        scrollDirection: Axis.horizontal,
        children: taskAnalysisValueNotifiers.value
            .map(
              (e) => Column(
                children: [
                  Row(
                    children: [
                      if (e[QuestionsStrings.index] != 1 &&
                          currentTaskIndex.value > 0)
                        ArrowButtonWidget(
                            icon: Icons.arrow_back_ios_outlined,
                            onPressed: () {
                              if (currentTaskIndex.value > 0) {
                                pageController.previousPage(
                                    duration: const Duration(milliseconds: 300),
                                    curve: Curves.easeInOutCubic);
                                currentTaskIndex.value--;
                              }
                            }),
                      context.smallGap,
                      Expanded(
                        child: Center(
                          child: ValueListenableBuilder<int>(
                            valueListenable: currentTaskIndex,
                            builder: (context, value, child) {
                              if (target.targetInfo?.tasks == null ||
                                  target.targetInfo?.tasks?.isEmpty == true) {
                                return const SizedBox();
                              }
                              return Text(
                                target.targetInfo?.tasks?[value].taskName ?? '',
                                style: context.title
                                    .copyWith(fontWeight: FontWeight.bold),
                              );
                            },
                          ),
                        ),
                      ),
                      ArrowButtonWidget(
                        icon: Icons.arrow_forward_ios_outlined,
                        onPressed: () async {
                          onNext();
                        },
                      )
                    ],
                  ),
                  context.largeGap,
                  TaskAnalysisChips(
                    target: target,
                    selectedValue: e[QuestionsStrings.selectedValue],
                    onNext: () {
                      e[QuestionsStrings.questionName].value = target
                          .targetInfo?.tasks?[currentTaskIndex.value].taskName;
                      onNext();
                    },
                  )
                ],
              ),
            )
            .toList(),
      ),
    );
  }
}

// class TaskAnalysisBody extends HookWidget {
//   final TargetModel target;
//   final ValueNotifier<List<Map<String, dynamic>>> taskAnalysisValueNotifiers;
//   final Function setState;
//   final bool showScore;
//
//   const TaskAnalysisBody(
//       {super.key,
//       required this.target,
//       required this.taskAnalysisValueNotifiers,
//       required this.setState,
//       required this.showScore});
//
//   @override
//   Widget build(BuildContext context) {
//     final pageController = usePageController();
//
//     void onNext() {
//       final index = taskAnalysisValueNotifiers.value.length;
//
//       final data = defaultTaskAnalysisMap(index + 1);
//
//       //? Add-DataPercent
//       taskAnalysisValueNotifiers.value.add(data);
//
//       pageController.nextPage(
//           duration: const Duration(milliseconds: 300),
//           curve: Curves.easeInOutCubic);
//
//       setState(() {});
//     }
//
//     return Visibility(
//       maintainState: true,
//       visible: !showScore,
//       child: ExpandablePageView(
//         controller: pageController,
//         physics: const NeverScrollableScrollPhysics(),
//         scrollDirection: Axis.horizontal,
//         children: taskAnalysisValueNotifiers.value
//             .map(
//               (e) => Column(
//                 children: [
//                   Row(
//                     children: [
//                       if (e[QuestionsStrings.index] != 1)
//                         ArrowButtonWidget(
//                             icon: Icons.arrow_back_ios_outlined,
//                             onPressed: () {
//                               pageController.previousPage(
//                                   duration: const Duration(milliseconds: 300),
//                                   curve: Curves.easeInOutCubic);
//                             }),
//                       context.smallGap,
//                       Expanded(
//                         child: Center(
//                           child: Text(
//                             target.title ?? '',
//                             style: context.title
//                                 .copyWith(fontWeight: FontWeight.bold),
//                           ),
//                         ),
//                       ),
//                       ArrowButtonWidget(
//                         icon: Icons.arrow_forward_ios_outlined,
//                         onPressed: () async {
//                           onNext();
//                         },
//                       )
//                     ],
//                   ),
//                   context.largeGap,
//                   TaskAnalysisChips(
//                     target: target,
//                     selectedValue: e[QuestionsStrings.selectedValue],
//                     onNext: onNext,
//                   )
//                 ],
//               ),
//             )
//             .toList(),
//       ),
//     );
//   }
// }
