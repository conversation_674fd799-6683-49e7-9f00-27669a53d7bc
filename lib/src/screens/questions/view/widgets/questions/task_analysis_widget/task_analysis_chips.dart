import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:good_thinker/src/screens/session/model/learning_tree_model.dart';
import 'package:good_thinker/src/shared/theme/color_manager.dart';
import 'package:xr_helper/xr_helper.dart';

class TaskAnalysisChips extends HookWidget {
  final TargetModel target;
  final ValueNotifier<String?> selectedValue;
  final Function? onNext;
  final Function(String)? onSelected;

  const TaskAnalysisChips({
    super.key,
    required this.target,
    required this.selectedValue,
    this.onNext,
    this.onSelected,
  });

  @override
  Widget build(BuildContext context) {
    final strategiesIds = target.criterias?.firstOrNull?.strategiesIds ?? [];
    const mainIds = StrategiesIds.mainIds;

    final isSelectedListMainIds =
        useState<List<bool>>(List.filled(mainIds.length, false));
    final isSelectedListStrategiesIds =
        useState<List<bool>>(List.filled(strategiesIds.length, false));

    Wrap buildWrap(
        List<StrategiesIds> list, ValueNotifier<List<bool>> isSelectedList) {
      return Wrap(
        alignment: WrapAlignment.start,
        runSpacing: 8,
        spacing: 8,
        children: list
            .asMap()
            .map((index, e) {
              final isSelected = selectedValue.value == e.value;

              return MapEntry(
                  index,
                  SizedBox(
                    child: ChoiceChip(
                      showCheckmark: false,
                      shape: RoundedRectangleBorder(
                          side: const BorderSide(
                              color: ColorManager.primaryColor),
                          borderRadius:
                              BorderRadius.circular(AppRadius.xLargeRadius)),
                      labelStyle: context.whiteLabelLarge.copyWith(
                          color: isSelected
                              ? ColorManager.white
                              : ColorManager.primaryColor),
                      label: FittedBox(child: Text(e.text ?? '')),
                      selected: isSelected,
                      onSelected: (bool selected) {
                        if (isSelectedList.value[index] == true) {
                          return;
                        }

                        isSelectedList.value = List.filled(list.length, false);
                        isSelectedList.value[index] = selected;

                        if (selected) {
                          selectedValue.value = e.value;
                        }

                        if (onNext != null) {
                          onNext!();
                        }
                        if (onSelected != null) {
                          onSelected!(selectedValue.value!);
                        }
                      },
                      selectedColor: ColorManager.primaryColor,
                    ),
                  ));
            })
            .values
            .toList(),
      );
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        buildWrap(mainIds, isSelectedListMainIds),
        buildWrap(strategiesIds, isSelectedListStrategiesIds),
      ],
    );
  }
}
// class TaskAnalysisChips extends HookWidget {
//   final TargetModel target;
//   final ValueNotifier<String?> selectedValue;
//   final Function? onNext;
//   final Function(String)? onSelected;
//
//   const TaskAnalysisChips({
//     super.key,
//     required this.target,
//     required this.selectedValue,
//     this.onNext,
//     this.onSelected,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     final strategiesIds = target.criterias?.firstOrNull?.strategiesIds ?? [];
//
//     const mainIds = StrategiesIds.mainIds;
//
//     final mergedList = [...mainIds, ...strategiesIds];
//
//     final isSelectedList =
//         useState<List<bool>>(List.filled(mergedList.length, false));
//
//     return Wrap(
//       alignment: WrapAlignment.start,
//       runSpacing: 8,
//       spacing: 8,
//       children: mergedList
//           .asMap()
//           .map((index, e) {
//             final isSelected = selectedValue.value == e.value;
//
//             return MapEntry(
//                 index,
//                 SizedBox(
//                   child: ChoiceChip(
//                     showCheckmark: false,
//                     shape: RoundedRectangleBorder(
//                         side:
//                             const BorderSide(color: ColorManager.primaryColor),
//                         borderRadius:
//                             BorderRadius.circular(AppRadius.xLargeRadius)),
//                     labelStyle: context.whiteLabelLarge.copyWith(
//                         color: isSelected
//                             ? ColorManager.white
//                             : ColorManager.primaryColor),
//                     label: FittedBox(child: Text(e.text ?? '')),
//                     selected: isSelected,
//                     onSelected: (bool selected) {
//                       //? if selected return
//                       if (isSelectedList.value[index] == true) {
//                         return;
//                       }
//
//                       isSelectedList.value =
//                           List.filled(mergedList.length, false);
//
//                       isSelectedList.value[index] = selected;
//
//                       if (selected) {
//                         selectedValue.value = e.value;
//                       }
//
//                       if (onNext != null) {
//                         onNext!();
//                       }
//                       if (onSelected != null) {
//                         onSelected!(selectedValue.value!);
//                       }
//                     },
//                     selectedColor: ColorManager.primaryColor,
//                   ),
//                 ));
//           })
//           .values
//           .toList(),
//     );
//   }
// }
