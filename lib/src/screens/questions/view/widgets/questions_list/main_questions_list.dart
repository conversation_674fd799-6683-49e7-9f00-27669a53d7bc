import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:good_thinker/src/screens/questions/model/question_model.dart';
import 'package:good_thinker/src/screens/questions/view/widgets/questions_list/questions_list.dart';
import 'package:good_thinker/src/screens/session/model/session_model.dart';
import 'package:good_thinker/src/shared/extensions/context_extensions.dart';
import 'package:good_thinker/src/shared/utils/default_question_values.dart';
import 'package:good_thinker/src/shared/utils/filtered_questions.dart';
import 'package:stop_watch_timer/stop_watch_timer.dart';
import 'package:xr_helper/xr_helper.dart';

class MainQuestionsList extends HookWidget {
  final SessionModel session;
  final DateTime? startTime;
  final DateTime? endTime;
  final StopWatchTimer stopWatchTimer;
  final bool isPaused;
  final ValueNotifier<QuestionsModel> questionModel;

  const MainQuestionsList({
    super.key,
    required this.session,
    required this.startTime,
    required this.endTime,
    required this.stopWatchTimer,
    required this.isPaused,
    required this.questionModel,
  });

  @override
  Widget build(BuildContext context) {
    final learningTrees = session.learningTrees ?? [];

    // * Data Percent Value Notifiers ===========================================
    defaultDataPercentValueNotifiers() =>
        useState<List<Map<String, dynamic>>>([defaultDataPercentMap()]);

    final dataPercentValueNotifiers =
        useState<List<ValueNotifier<List<Map<String, dynamic>>>>>(List.generate(
            learningTrees.dataPercentQuestions.length,
            (_) => defaultDataPercentValueNotifiers()));

    // * Data Sampling Value Notifiers ===========================================
    defaultDataSamplingValueNotifiers() =>
        useState<List<Map<String, dynamic>>>([defaultDataSamplingMap()]);

    final dataSamplingValueNotifiers =
        useState<List<ValueNotifier<List<Map<String, dynamic>>>>>(List.generate(
            learningTrees.dataSamplingQuestions.length,
            (_) => defaultDataSamplingValueNotifiers()));

    // * Data Prompt Value Notifiers ===========================================
    defaultDataPromptValueNotifiers() =>
        useState<List<Map<String, dynamic>>>([defaultDataPromptMap()]);

    final dataPromptValueNotifiers =
        useState<List<ValueNotifier<List<Map<String, dynamic>>>>>(List.generate(
            learningTrees.dataPromptQuestions.length,
            (_) => defaultDataPromptValueNotifiers()));

    // * Data Task Value Notifiers ===========================================
    defaultTargetAnalysisValueNotifiers() =>
        useState<List<Map<String, dynamic>>>([defaultTaskAnalysisMap()]);

    final dataTaskValueNotifiers =
        useState<List<ValueNotifier<List<Map<String, dynamic>>>>>(List.generate(
            learningTrees.dataTaskQuestions.length,
            (_) => defaultTargetAnalysisValueNotifiers()));

    // * Duration Value Notifiers ===========================================
    defaultDurationValueNotifiers() =>
        useState<Map<String, dynamic>>(defaultDurationMap());

    final durationValueNotifiers =
        useState<List<ValueNotifier<Map<String, dynamic>>>>(List.generate(
            learningTrees.dataDurationQuestions.length,
            (_) => defaultDurationValueNotifiers()));

    // * Rating Scale Value Notifiers ===========================================
    defaultRatingScaleValueNotifiers() =>
        useState<Map<String, dynamic>>(defaultRatingMap());

    final ratingScaleValueNotifiers =
        useState<List<ValueNotifier<Map<String, dynamic>>>>(List.generate(
            learningTrees.dataRatingQuestions.length,
            (_) => defaultRatingScaleValueNotifiers()));

    // * Data Value Notifiers ===========================================
    defaultDataValueNotifiers() =>
        useState<Map<String, dynamic>>(defaultDataMap());

    final dataValueNotifiers =
        useState<List<ValueNotifier<Map<String, dynamic>>>>(List.generate(
            learningTrees.dataQuestions.length,
            (_) => defaultDataValueNotifiers()));

    // * Data Frequency 2 Value Notifiers ===========================================
    defaultDataFrequency2ValueNotifiers() =>
        useState<Map<String, dynamic>>(defaultDataFrequency2Map());

    final dataFrequency2ValueNotifiers =
        useState<List<ValueNotifier<Map<String, dynamic>>>>(List.generate(
            learningTrees.dataFrequency2Questions.length,
            (_) => defaultDataFrequency2ValueNotifiers()));

    // * Data Frequency Value Notifiers ===========================================
    defaultDataFrequencyValueNotifiers() =>
        useState<Map<String, dynamic>>(defaultDataFrequencyMap());

    final dataFrequencyValueNotifiers =
        useState<List<ValueNotifier<Map<String, dynamic>>>>(List.generate(
            learningTrees.dataFrequencyQuestions.length,
            (_) => defaultDataFrequencyValueNotifiers()));

    final duration = StopWatchTimer.getDisplayTime(
      stopWatchTimer.rawTime.value,
      minute: true,
      second: true,
      hours: true,
      milliSecond: false,
    );

    // * Questions Model ===========================================
    WidgetsBinding.instance.addPostFrameCallback((_) {
      questionModel.value = QuestionsModel(
        dataPercentValueNotifiers: dataPercentValueNotifiers.value,
        dataPromptValueNotifiers: dataPromptValueNotifiers.value,
        dataTaskValueNotifiers: dataTaskValueNotifiers.value,
        dataDurationValueNotifiers: durationValueNotifiers.value,
        dataRatingValueNotifiers: ratingScaleValueNotifiers.value,
        dataValueNotifiers: dataValueNotifiers.value,
        dataSamplingValueNotifiers: dataSamplingValueNotifiers.value,
        dataFrequencyValueNotifiers: dataFrequencyValueNotifiers.value,
        dataFrequency2ValueNotifiers: dataFrequency2ValueNotifiers.value,
        session: session,
        startTime: startTime,
        endTime: endTime,
        duration: duration,
      );
    });

    final cannotAnswer = (isPaused || startTime == null) &&
        (endTime == null || endTime!.isAfter(DateTime.now()));

    return GestureDetector(
      onTap: () {
        if (cannotAnswer) {
          context.showBarMessage(context.tr.pleaseStartTimerFirst,
              isError: true);
        }
      },
      child: Stack(
        children: [
          Container(
            color: Colors.transparent,
            child: IgnorePointer(
              ignoring: cannotAnswer,
              child: QuestionsList(
                questions: questionModel.value,
                endTime: endTime,
                session: session,
              ),
            ),
          ),
          if (cannotAnswer)
            Container(
              color: Colors.black.withOpacity(0.5),
            )
        ],
      ),
    );
  }
}
