import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:good_thinker/src/screens/questions/model/question_model.dart';
import 'package:good_thinker/src/screens/questions/view/widgets/questions/data_duration_widget/data_duration_widget.dart';
import 'package:good_thinker/src/screens/questions/view/widgets/questions/data_frequency2_widget/data_frequency2_widget.dart';
import 'package:good_thinker/src/screens/questions/view/widgets/questions/data_frequency_widget/data_frequency_widget.dart';
import 'package:good_thinker/src/screens/questions/view/widgets/questions/data_percent_widget/data_percent_widget.dart';
import 'package:good_thinker/src/screens/questions/view/widgets/questions/data_prompt_widget/data_prompt_widget.dart';
import 'package:good_thinker/src/screens/questions/view/widgets/questions/data_rating_widget/data_rating_widget.dart';
import 'package:good_thinker/src/screens/questions/view/widgets/questions/data_sampling_widget/data_sampling_widget.dart';
import 'package:good_thinker/src/screens/questions/view/widgets/questions/data_widget/data_widget.dart';
import 'package:good_thinker/src/screens/questions/view/widgets/questions/task_analysis_widget/task_analysis_widget.dart';
import 'package:good_thinker/src/screens/session/model/session_model.dart';
import 'package:good_thinker/src/shared/consts/questions_strings.dart';
import 'package:good_thinker/src/shared/extensions/context_extensions.dart';
import 'package:good_thinker/src/shared/utils/filtered_questions.dart';
import 'package:good_thinker/src/shared/widgets/list_view/base_list_view.dart';
import 'package:stop_watch_timer/stop_watch_timer.dart';
import 'package:xr_helper/xr_helper.dart';

class QuestionsList extends ConsumerWidget {
  final QuestionsModel questions;
  final SessionModel session;
  final DateTime? endTime;

  const QuestionsList({
    super.key,
    required this.questions,
    required this.session,
    required this.endTime,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final learningTrees = session.learningTrees ?? [];

    return Column(
      children: [
        GestureDetector(
          onTap: () {
            if (endTime != null) {
              context.showBarMessage(context.tr.dataCollectionEnded,
                  isError: true);
            }
          },
          child: Container(
            color: Colors.transparent,
            child: IgnorePointer(
              ignoring: endTime != null,
              child: ListView(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                children: [
                  // * Data Percent ===========================================
                  BaseListView(
                      itemCount: learningTrees.dataPercentQuestions.length,
                      itemBuilder: (context, index) {
                        if (questions.dataPercentValueNotifiers.isEmpty) {
                          return const SizedBox.shrink();
                        }

                        return DataPercentWidget(
                          target: learningTrees.dataPercentQuestions[index],
                          dataPercentValueNotifiers:
                              questions.dataPercentValueNotifiers[index],
                        );
                      }),

                  context.smallGap,

                  // * Data Prompt ===========================================
                  BaseListView(
                      itemCount: learningTrees.dataPromptQuestions.length,
                      itemBuilder: (context, index) {
                        if (questions.dataPromptValueNotifiers.isEmpty) {
                          return const SizedBox.shrink();
                        }
                        return DataPromptWidget(
                          target: learningTrees.dataPromptQuestions[index],
                          dataPromptValueNotifiers:
                              questions.dataPromptValueNotifiers[index],
                        );
                      }),

                  context.smallGap,

                  // * Task Analysis ===========================================
                  BaseListView(
                      itemCount: learningTrees.dataTaskQuestions.length,
                      itemBuilder: (context, index) {
                        if (questions.dataTaskValueNotifiers.isEmpty) {
                          return const SizedBox.shrink();
                        }
                        return TaskAnalysisWidget(
                          target: learningTrees.dataTaskQuestions[index],
                          taskAnalysisValueNotifiers:
                              questions.dataTaskValueNotifiers[index],
                        );
                      }),

                  context.smallGap,

                  // * Duration ===========================================
                  BaseListView(
                      itemCount: learningTrees.dataDurationQuestions.length,
                      itemBuilder: (context, index) {
                        if (questions.dataDurationValueNotifiers.isEmpty) {
                          return const SizedBox.shrink();
                        }
                        return DataDurationWidget(
                          target: learningTrees.dataDurationQuestions[index],
                          durationValue: questions
                                  .dataDurationValueNotifiers[index]
                                  .value[QuestionsStrings.selectedDurationTime]
                              as ValueNotifier<StopWatchTimer>,
                        );
                      }),

                  context.smallGap,

                  // * Rating Scale ===========================================
                  BaseListView(
                      itemCount: learningTrees.dataRatingQuestions.length,
                      itemBuilder: (context, index) {
                        if (questions.dataRatingValueNotifiers.isEmpty) {
                          return const SizedBox.shrink();
                        }
                        return DataRatingWidget(
                          target: learningTrees.dataRatingQuestions[index],
                          valueNotifier: questions
                                  .dataRatingValueNotifiers[index]
                                  .value[QuestionsStrings.selectedRating]
                              as ValueNotifier<double>,
                        );
                      }),

                  context.smallGap,

                  // * Score ===========================================
                  BaseListView(
                      itemCount: learningTrees.dataQuestions.length,
                      itemBuilder: (context, index) {
                        if (questions.dataValueNotifiers.isEmpty) {
                          return const SizedBox.shrink();
                        }
                        return DataWidget(
                          target: learningTrees.dataQuestions[index],
                          valueNotifier: questions.dataValueNotifiers[index]
                                  .value[QuestionsStrings.score]
                              as ValueNotifier<num>,
                        );
                      }),

                  context.smallGap,

                  // * Data Sampling ===========================================
                  BaseListView(
                      itemCount: learningTrees.dataSamplingQuestions.length,
                      itemBuilder: (context, index) {
                        if (questions.dataSamplingValueNotifiers.isEmpty) {
                          return const SizedBox.shrink();
                        }
                        return DataSamplingWidget(
                          target: learningTrees.dataSamplingQuestions[index],
                          dataSamplingValueNotifiers:
                              questions.dataSamplingValueNotifiers[index],
                        );
                      }),

                  context.smallGap,

                  // * Frequency ===========================================
                  BaseListView(
                      itemCount: learningTrees.dataFrequencyQuestions.length,
                      itemBuilder: (context, index) {
                        if (questions.dataFrequencyValueNotifiers.isEmpty) {
                          return const SizedBox.shrink();
                        }
                        return DataFrequencyWidget(
                            target: learningTrees.dataFrequencyQuestions[index],
                            valueNotifiers: questions
                                .dataFrequencyValueNotifiers[index].value);
                      }),

                  context.smallGap,

                  // * Frequency 2 ===========================================
                  BaseListView(
                      itemCount: learningTrees.dataFrequency2Questions.length,
                      itemBuilder: (context, index) {
                        if (questions.dataFrequency2ValueNotifiers.isEmpty) {
                          return const SizedBox.shrink();
                        }
                        return DataFrequency2Widget(
                          target: learningTrees.dataFrequency2Questions[index],
                          valueNotifier: questions
                                  .dataFrequency2ValueNotifiers[index]
                                  .value[QuestionsStrings.selectedFrequency]
                              as ValueNotifier<num>,
                        );
                      }),
                ],
              ),
            ),
          ),
        ),

        context.smallGap,

        // * End Data Collection Button ===========================================
        // EndDataCollectionButton(
        //   session,
        //   questionModel: questions,
        // ).paddingAll(AppSpaces.mediumPadding),
      ],
    );
  }
}
