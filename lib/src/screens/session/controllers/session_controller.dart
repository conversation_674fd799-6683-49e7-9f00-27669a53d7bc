import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:good_thinker/src/screens/auth/repositories/auth_repository.dart';
import 'package:good_thinker/src/screens/auth/views/login/login_screen.dart';
import 'package:good_thinker/src/screens/home/<USER>/home_screen.dart';
import 'package:good_thinker/src/screens/session/repositories/session_local_repository.dart';
import 'package:good_thinker/src/screens/session/repositories/session_remote_repository.dart';
import 'package:good_thinker/src/shared/consts/network/api_strings.dart';
import 'package:good_thinker/src/shared/extensions/context_extensions.dart';
import 'package:xr_helper/xr_helper.dart';

import '../model/session_model.dart';

final sessionControllerProvider =
    Provider.family<SessionController, BuildContext>((ref, context) {
  final authRemoteRepository = ref.watch(authRepoProvider);

  final sessionRemoteRepository = ref.watch(sessionRepoProvider);
  final sessionLocalRepository = ref.watch(sessionLocalRepoProvider);

  return SessionController(context,
      authRemoteRepository: authRemoteRepository,
      sessionRepository: sessionRemoteRepository,
      sessionLocalRepository: sessionLocalRepository);
});

final getSessionsDataProvider =
    FutureProvider.family<List<SessionModel>?, BuildContext>(
        (ref, context) async {
  final sessionController = ref.watch(sessionControllerProvider(context));

  return await sessionController.getSessions();
});

class SessionController extends BaseVM {
  final BuildContext context;
  final SessionRemoteRepository sessionRepository;
  final SessionLocalRepository sessionLocalRepository;
  final AuthRepository authRemoteRepository;

  SessionController(
    this.context, {
    required this.sessionLocalRepository,
    required this.sessionRepository,
    required this.authRemoteRepository,
  });

  //? Get Sessions ======================================================
  Future<List<SessionModel>?> getSessions({
    bool refresh = false,
  }) async {
    return await baseFunction(context, () async {
      try {
        if (!refresh) {
          final localSessions = await sessionLocalRepository.getSessions();

          if (localSessions.isNotEmpty) {
            return localSessions;
          }
        }

        final remoteSessions = await sessionRepository.getSessions();

        return remoteSessions;
      } catch (e) {
        if (e.toString().contains(ApiStrings.unAuthenticated)) {
          GetStorageService.clearLocalData();
          context.toReplacement(const LoginScreen());
        }
      }
    }, onError: (errorMsg) async {
      Log.e('ErrorWhile getting sessions $errorMsg');
      if (errorMsg == "Unauthenticated" || errorMsg == "Unauthorized") {
        await authRemoteRepository.refreshToken();

        context.toReplacement(const HomeScreen());

        context.showBarMessage(
          context.tr
              .yourSessionHasRefreshedSuccessfullyPleaseReloadAgainToSendData,
          isError: true,
        );
      }
    });
  }
}
