import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti_tickets/src/core/shared/widgets/navigations/controller/bottom_nav_bar.controller.dart';
import 'package:opti_tickets/src/screens/auth/models/user_model.dart';
import 'package:opti_tickets/src/screens/home/<USER>/home_screen.dart';

import '../../../core/shared/widgets/navigations/bottom_nav_bar.widget.dart';
import '../../reports_screen/view/reports_screen.dart';

class SelectedScreen extends ConsumerWidget {
  const SelectedScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedIndex = ref.watch(bottomNavigationControllerProvider);
    final isAdmin = UserModel.currentUser().isAdmin;

    return Scaffold(
      bottomNavigationBar: isAdmin ? const BottomNavBarWidget() : null,
      body: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        transitionBuilder: (Widget child, Animation<double> animation) {
          return FadeTransition(opacity: animation, child: child);
        },
        child: selectedIndex == 0 ? const HomeScreen() : const ReportsScreen(),
      ),
    );
  }
}
