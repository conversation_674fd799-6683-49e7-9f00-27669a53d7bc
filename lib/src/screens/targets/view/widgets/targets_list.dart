import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:good_thinker/src/screens/session/model/learning_tree_model.dart';
import 'package:good_thinker/src/screens/session/model/session_model.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import 'target_card.dart';

class TargetsList extends ConsumerWidget {
  final List<TargetModel> learningTree;
  final SessionModel session;
  final ValueNotifier<bool> showQuestions;

  const TargetsList(
      {super.key,
      required this.learningTree,
      required this.session,
      required this.showQuestions});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ListView.separated(
      shrinkWrap: true,
      separatorBuilder: (context, index) => context.smallGap,
      padding: const EdgeInsets.only(bottom: AppSpaces.smallPadding),
      itemBuilder: (context, index) => TargetCard(
        session: session,
        learningTree: learningTree[index],
        showQuestions: showQuestions,
      ),
      itemCount: learningTree.length,
    );
  }
}
