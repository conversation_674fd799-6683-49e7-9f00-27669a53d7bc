import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:good_thinker/src/shared/consts/local/hive_keys.dart';
import 'package:hive/hive.dart';
import 'package:xr_helper/xr_helper.dart';

final hiveProvider = Provider<HiveService>((ref) {
  return HiveService();
});

class HiveService {
  Future<Box> openBox({required String boxName}) async =>
      await Hive.openBox(boxName);

  Future<void> saveData(String boxName, {required dynamic data}) async {
    if (Hive.isBoxOpen(boxName)) {
      await Hive.box(boxName).add(data);
    } else {
      await openBox(boxName: boxName).then((box) => box.add(data));
    }

    Log.w(' Save =================== $boxName $data');
  }

  Future<void> putData(String boxName,
      {required dynamic key, required dynamic data}) async {
    if (Hive.isBoxOpen(boxName)) {
      await Hive.box(boxName).put(key, data);
    } else {
      await openBox(boxName: boxName).then((box) => box.put(key, data));
      Log.w('Put===================$boxName');
    }
  }

  Future<Map<dynamic, dynamic>> getData({required String boxName}) async {
    Map<dynamic, dynamic> data = {};

    if (Hive.isBoxOpen(boxName)) {
      data = Hive.box(boxName).toMap();

      Log.f(' Get =============== ${data}');
    } else {
      final box = await openBox(boxName: boxName);

      data = box.toMap();
      Log.f(' Get =============== ${data}');
    }
    return data;
  }

  Future<void> delete(String boxName, {required int index}) async {
    if (Hive.isBoxOpen(boxName)) {
      await Hive.box(boxName).deleteAt(index);
    } else {
      await Hive.openBox(boxName).then((box) => box.deleteAt(index));
    }
  }

  Future<void> clearAllData() async {
    await Hive.deleteBoxFromDisk(HiveKeys.travelTime);
    await Hive.deleteBoxFromDisk(HiveKeys.questions);
    await Hive.deleteBoxFromDisk(HiveKeys.sessions);
    await Hive.deleteBoxFromDisk(HiveKeys.participant);
    await Hive.deleteBoxFromDisk(HiveKeys.clintSignature);
    await Hive.deleteBoxFromDisk(HiveKeys.providerSignature);
    await Hive.deleteBoxFromDisk(HiveKeys.note);
    await Hive.deleteBoxFromDisk(HiveKeys.pauseTime);
    await Hive.deleteBoxFromDisk(HiveKeys.duration);

    await GetStorageService.removeLocalData(key: LocalKeys.signatureName);
    await GetStorageService.removeLocalData(key: LocalKeys.sessionLength);

    await Hive.deleteFromDisk();
  }

  Future<void> clear(String boxName) async {
    if (Hive.isBoxOpen(boxName)) {
      await Hive.box(boxName).clear();
    } else {
      await Hive.openBox(boxName).then((box) => box.clear());
    }
  }
}
